# Nginx configuration for large file uploads
# Add this to your nginx server block

# Increase client max body size for file uploads
client_max_body_size 512M;

# Increase timeouts for large file uploads
client_body_timeout 300s;
client_header_timeout 300s;
send_timeout 300s;
proxy_read_timeout 300s;
proxy_connect_timeout 300s;
proxy_send_timeout 300s;

# Buffer settings for large uploads
client_body_buffer_size 128k;
client_header_buffer_size 1k;
large_client_header_buffers 4 4k;

# Example server block:
# server {
#     listen 80;
#     server_name your-domain.com;
#     root /path/to/your/laravel/public;
#     index index.php;
#     
#     # Include the upload settings above
#     client_max_body_size 512M;
#     client_body_timeout 300s;
#     
#     location / {
#         try_files $uri $uri/ /index.php?$query_string;
#     }
#     
#     location ~ \.php$ {
#         fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
#         fastcgi_index index.php;
#         fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
#         include fastcgi_params;
#         
#         # PHP upload settings
#         fastcgi_read_timeout 300;
#         fastcgi_send_timeout 300;
#     }
# }
