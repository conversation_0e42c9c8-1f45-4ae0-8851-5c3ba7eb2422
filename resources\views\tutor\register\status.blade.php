@extends('layouts.app')

@section('title', 'Status Aplikasi Tutor')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Main Content -->
        <div class="bg-white rounded-lg shadow-sm border p-8">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Status Aplikasi Tutor</h1>
                <p class="text-gray-600">Pantau perkembangan aplikasi Anda menjadi tutor</p>
            </div>

            <!-- Status Card -->
            <div class="max-w-2xl mx-auto">
                @if($profile->status === 'submitted' || $profile->status === 'under_review')
                    <!-- Pending/Under Review Status -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-8 text-center">
                        <div class="flex justify-center mb-4">
                            <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <h2 class="text-2xl font-bold text-blue-900 mb-2">Aplikasi Sedang Ditinjau</h2>
                        <p class="text-blue-800 mb-4">
                            Aplikasi Anda telah berhasil dikirim dan sedang dalam proses peninjauan oleh tim kami.
                        </p>
                        <div class="bg-white rounded-lg p-4 mb-4">
                            <div class="flex justify-between items-center text-sm">
                                <span class="text-gray-600">Tanggal Pengajuan:</span>
                                <span class="font-medium">{{ $profile->submitted_at->format('d M Y, H:i') }}</span>
                            </div>
                        </div>
                        <p class="text-blue-700 text-sm">
                            Estimasi waktu review: 1-3 hari kerja. Anda akan menerima notifikasi melalui email.
                        </p>
                    </div>

                @elseif($profile->status === 'approved')
                    <!-- Approved Status -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-8 text-center">
                        <div class="flex justify-center mb-4">
                            <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                        </div>
                        <h2 class="text-2xl font-bold text-green-900 mb-2">Selamat! Aplikasi Disetujui</h2>
                        <p class="text-green-800 mb-4">
                            Aplikasi Anda telah disetujui. Sekarang Anda dapat mulai membuat dan menjual kursus di platform kami.
                        </p>
                        <div class="bg-white rounded-lg p-4 mb-6">
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Tanggal Pengajuan:</span>
                                    <span class="font-medium">{{ $profile->submitted_at->format('d M Y, H:i') }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Tanggal Disetujui:</span>
                                    <span class="font-medium">{{ $profile->reviewed_at->format('d M Y, H:i') }}</span>
                                </div>
                            </div>
                        </div>
                        <a href="{{ route('tutor.dashboard') }}"
                           class="btn btn-primary px-8 py-3 inline-block">
                            Masuk ke Dashboard Tutor
                        </a>
                    </div>

                @elseif($profile->status === 'rejected')
                    <!-- Rejected Status -->
                    <div class="bg-red-50 border border-red-200 rounded-lg p-8 text-center">
                        <div class="flex justify-center mb-4">
                            <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </div>
                        </div>
                        <h2 class="text-2xl font-bold text-red-900 mb-2">Aplikasi Ditolak</h2>
                        <p class="text-red-800 mb-4">
                            Maaf, aplikasi Anda tidak dapat disetujui saat ini.
                        </p>

                        @if($profile->rejection_reason)
                        <div class="bg-white rounded-lg p-4 mb-4 text-left">
                            <h3 class="font-semibold text-gray-900 mb-2">Alasan Penolakan:</h3>
                            <p class="text-gray-700 text-sm">{{ $profile->rejection_reason }}</p>
                        </div>
                        @endif

                        <div class="bg-white rounded-lg p-4 mb-6">
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Tanggal Pengajuan:</span>
                                    <span class="font-medium">{{ $profile->submitted_at->format('d M Y, H:i') }}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Tanggal Ditolak:</span>
                                    <span class="font-medium">{{ $profile->reviewed_at->format('d M Y, H:i') }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <p class="text-red-700 text-sm">
                                Anda dapat memperbaiki profil dan mengajukan ulang aplikasi.
                            </p>
                            <a href="{{ route('tutor.register.profile') }}"
                               class="btn btn-primary px-8 py-3 inline-block">
                                Perbaiki Profil
                            </a>
                        </div>
                    </div>

                @else
                    <!-- Draft Status -->
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
                        <div class="flex justify-center mb-4">
                            <div class="w-16 h-16 bg-gray-500 rounded-full flex items-center justify-center">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Aplikasi Belum Lengkap</h2>
                        <p class="text-gray-600 mb-6">
                            Lengkapi profil Anda untuk melanjutkan proses pendaftaran sebagai tutor.
                        </p>
                        <a href="{{ route('tutor.register.terms') }}"
                           class="btn btn-primary px-8 py-3 inline-block">
                            Lanjutkan Pendaftaran
                        </a>
                    </div>
                @endif

                <!-- Additional Information -->
                <div class="mt-8 bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Informasi Kontak</h3>
                    <p class="text-gray-600 text-sm mb-2">
                        Jika Anda memiliki pertanyaan tentang aplikasi tutor, silakan hubungi kami:
                    </p>
                    <div class="space-y-1 text-sm">
                        <p class="text-gray-700">
                            <span class="font-medium">Email:</span> <EMAIL>
                        </p>
                        <p class="text-gray-700">
                            <span class="font-medium">WhatsApp:</span> +62 812-3456-7890
                        </p>
                    </div>
                </div>

                <!-- Back to Home -->
                <div class="mt-6 text-center">
                    <a href="{{ route('home') }}"
                       class="text-primary hover:text-primary-dark font-medium">
                        ← Kembali ke Beranda
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
