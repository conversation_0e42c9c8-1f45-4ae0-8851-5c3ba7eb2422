<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();

            // Profile Information
            $table->string('profile_picture')->nullable(); // Profile picture path
            $table->text('bio')->nullable(); // Short bio for all users
            $table->string('location')->nullable(); // City/Country
            $table->string('website')->nullable(); // Personal website

            // Social Media Links
            $table->string('linkedin_url')->nullable();
            $table->string('github_url')->nullable();
            $table->string('twitter_url')->nullable();
            $table->string('instagram_url')->nullable();
            $table->string('youtube_url')->nullable();
            $table->string('facebook_url')->nullable();

            // Professional Information
            $table->string('job_title')->nullable(); // Current job title
            $table->string('company')->nullable(); // Current company
            $table->json('skills')->nullable(); // Array of skills

            // Education & Learning
            $table->enum('pendidikan', ['sma', 'diploma', 's1', 's2', 's3'])->nullable(); // Education level
            $table->json('minat_belajar')->nullable(); // Learning interests array
            $table->string('institusi_pendidikan')->nullable(); // Educational institution
            $table->string('jurusan')->nullable(); // Major/field of study
            $table->year('tahun_lulus')->nullable(); // Graduation year

            // AI Career Assistant Data
            $table->json('career_goals')->nullable(); // Short-term and long-term career goals
            $table->json('work_experience')->nullable(); // Work experience history
            $table->json('certifications')->nullable(); // Professional certifications
            $table->json('learning_preferences')->nullable(); // Learning style preferences
            $table->json('personality_traits')->nullable(); // Personality assessment results
            $table->integer('experience_years')->nullable(); // Total years of experience
            $table->json('industry_interests')->nullable(); // Industries of interest
            $table->json('salary_expectations')->nullable(); // Salary range expectations
            $table->json('work_preferences')->nullable(); // Remote, hybrid, onsite preferences
            $table->json('ai_recommendations')->nullable(); // AI-generated career recommendations
            $table->timestamp('last_ai_analysis')->nullable(); // Last AI career analysis date

            $table->string('password');

            // Social Authentication
            $table->string('google_id')->nullable();

            // User Roles
            $table->boolean('is_tutor')->default(false);
            $table->boolean('is_admin')->default(false);
            $table->boolean('is_superadmin')->default(false);
            $table->enum('tutor_status', ['pending', 'approved', 'rejected', 'inactive'])->nullable();
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
