/**
 * AI Chat Component for Ngambiskuy
 * Intelligent Course Engine (ICE) Integration
 * 
 * This module handles the AI chat functionality including:
 * - Course recommendations
 * - Career path analysis
 * - Personalized learning paths
 * - Integration with Gemini AI (to be implemented)
 */

class NgambiskuyAIChat {
    constructor() {
        this.isOpen = false;
        this.messageHistory = [];
        this.isTyping = false;
        this.apiEndpoint = '/api/ai-chat'; // Backend endpoint for Gemini AI
        this.userProfile = null;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadUserProfile();
        this.initializeWelcomeMessage();
    }
    
    bindEvents() {
        const chatToggle = document.getElementById('ai-chat-toggle');
        const chatInput = document.getElementById('chat-input');
        const sendButton = document.getElementById('send-message');
        const quickActionBtns = document.querySelectorAll('.quick-action-btn');
        
        if (chatToggle) {
            chatToggle.addEventListener('click', () => this.toggleChat());
        }
        
        if (chatInput) {
            chatInput.addEventListener('input', (e) => this.handleInputChange(e));
            chatInput.addEventListener('keypress', (e) => this.handleKeyPress(e));
        }
        
        if (sendButton) {
            sendButton.addEventListener('click', () => this.sendMessage());
        }
        
        quickActionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleQuickAction(e));
        });
        
        // Close chat when clicking outside
        document.addEventListener('click', (e) => this.handleOutsideClick(e));
    }
    
    toggleChat() {
        const chatWindow = document.getElementById('ai-chat-window');
        const chatIcon = document.getElementById('chat-icon');
        const closeIcon = document.getElementById('close-icon');
        
        this.isOpen = !this.isOpen;
        
        if (this.isOpen) {
            chatWindow.classList.remove('hidden');
            setTimeout(() => {
                chatWindow.classList.remove('scale-0', 'opacity-0');
                chatWindow.classList.add('scale-100', 'opacity-100');
            }, 10);
            document.getElementById('chat-input').focus();
            chatIcon.classList.add('hidden');
            closeIcon.classList.remove('hidden');
            
            // Hide notification badge when chat is opened
            this.hideNotification();
        } else {
            chatWindow.classList.remove('scale-100', 'opacity-100');
            chatWindow.classList.add('scale-0', 'opacity-0');
            setTimeout(() => {
                chatWindow.classList.add('hidden');
            }, 300);
            chatIcon.classList.remove('hidden');
            closeIcon.classList.add('hidden');
        }
    }
    
    handleInputChange(e) {
        const length = e.target.value.length;
        const charCount = document.getElementById('char-count');
        const sendButton = document.getElementById('send-message');
        
        charCount.textContent = `${length}/500`;
        sendButton.disabled = length === 0;
        
        if (length > 450) {
            charCount.classList.add('text-red-500');
        } else {
            charCount.classList.remove('text-red-500');
        }
    }
    
    handleKeyPress(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.sendMessage();
        }
    }
    
    handleQuickAction(e) {
        const action = e.target.textContent.trim();
        const chatInput = document.getElementById('chat-input');
        const sendButton = document.getElementById('send-message');
        
        chatInput.value = this.getQuickActionMessage(action);
        chatInput.focus();
        sendButton.disabled = false;
        
        // Update character count
        const event = new Event('input');
        chatInput.dispatchEvent(event);
    }
    
    handleOutsideClick(e) {
        const chatToggle = document.getElementById('ai-chat-toggle');
        const chatWindow = document.getElementById('ai-chat-window');
        
        if (this.isOpen && 
            !chatToggle.contains(e.target) && 
            !chatWindow.contains(e.target)) {
            this.toggleChat();
        }
    }
    
    getQuickActionMessage(action) {
        const messages = {
            'Rekomendasi Kursus': 'Bisakah Anda merekomendasikan kursus yang tepat untuk saya?',
            'Jalur Karir': 'Saya ingin tahu jalur karir yang cocok dengan skill saya',
            'Bantuan': 'Saya butuh bantuan menggunakan platform Ngambiskuy'
        };
        return messages[action] || action;
    }
    
    async sendMessage() {
        const chatInput = document.getElementById('chat-input');
        const message = chatInput.value.trim();
        
        if (!message || this.isTyping) return;
        
        // Add user message
        this.addMessage(message, 'user');
        
        // Clear input
        chatInput.value = '';
        document.getElementById('char-count').textContent = '0/500';
        document.getElementById('send-message').disabled = true;
        
        // Show typing indicator
        this.showTypingIndicator();
        this.isTyping = true;
        
        try {
            // Call AI service (Gemini AI integration point)
            const response = await this.callAIService(message);
            this.hideTypingIndicator();
            this.addMessage(response, 'ai');
        } catch (error) {
            console.error('AI Chat Error:', error);
            this.hideTypingIndicator();
            this.addMessage('Maaf, terjadi kesalahan. Silakan coba lagi nanti.', 'ai');
        } finally {
            this.isTyping = false;
        }
    }
    
    async callAIService(message) {
        // TODO: Replace with actual Gemini AI integration
        // This is a placeholder that simulates AI response
        
        return new Promise((resolve) => {
            setTimeout(() => {
                const response = this.generatePlaceholderResponse(message);
                resolve(response);
            }, 1500 + Math.random() * 1000);
        });
        
        /* 
        // Future Gemini AI integration:
        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    message: message,
                    context: this.getConversationContext(),
                    user_profile: this.userProfile
                })
            });
            
            const data = await response.json();
            return data.response;
        } catch (error) {
            throw new Error('Failed to get AI response');
        }
        */
    }
    
    generatePlaceholderResponse(userMessage) {
        const lowerMessage = userMessage.toLowerCase();
        
        // Intelligent Course Engine responses
        if (lowerMessage.includes('rekomendasi') || lowerMessage.includes('kursus')) {
            return this.getCourseRecommendation();
        }
        
        if (lowerMessage.includes('karir') || lowerMessage.includes('pekerjaan')) {
            return this.getCareerPathAdvice();
        }
        
        if (lowerMessage.includes('belajar') || lowerMessage.includes('pembelajaran')) {
            return this.getLearningPathSuggestion();
        }
        
        if (lowerMessage.includes('bantuan') || lowerMessage.includes('help')) {
            return this.getHelpResponse();
        }
        
        // Default response
        return 'Terima kasih atas pertanyaan Anda! Saya adalah AI Assistant Ngambiskuy yang dapat membantu dengan rekomendasi kursus, analisis jalur karir, dan panduan pembelajaran. Apa yang ingin Anda ketahui?';
    }
    
    getCourseRecommendation() {
        const recommendations = [
            'Berdasarkan tren industri 2024, saya merekomendasikan kursus Frontend Development dengan React. Kursus ini memiliki tingkat penempatan kerja 85% dan gaji rata-rata Rp 8-15 juta.',
            'Untuk pemula, saya sarankan mulai dengan "Fundamental Programming" kemudian lanjut ke "Web Development Bootcamp". Jalur ini dirancang khusus untuk mencapai job-ready dalam 3-6 bulan.',
            'Data Science sedang sangat diminati! Kursus "Python for Data Analysis" kami memiliki partnership dengan 50+ perusahaan untuk penempatan kerja langsung.'
        ];
        
        return recommendations[Math.floor(Math.random() * recommendations.length)];
    }
    
    getCareerPathAdvice() {
        const careerAdvice = [
            'Berdasarkan analisis skill gap Indonesia, posisi yang paling dibutuhkan adalah: Frontend Developer (600+ lowongan/bulan), Data Analyst (400+ lowongan), dan UI/UX Designer (300+ lowongan). Mana yang menarik bagi Anda?',
            'Untuk menjadi Full Stack Developer, Anda perlu menguasai: Frontend (React/Vue), Backend (Node.js/Laravel), Database (MySQL/MongoDB), dan DevOps basics. Estimasi waktu: 6-9 bulan dengan pembelajaran intensif.',
            'Career Path Predictor menunjukkan Anda 70% cocok untuk posisi Data Scientist. Skill yang perlu ditingkatkan: Python advanced, Machine Learning, dan SQL. Mau saya buatkan roadmap detailnya?'
        ];
        
        return careerAdvice[Math.floor(Math.random() * careerAdvice.length)];
    }
    
    getLearningPathSuggestion() {
        return 'Intelligent Course Engine (ICE) telah menganalisis profil Anda. Rekomendasi pembelajaran personal: 1) Mulai dengan "JavaScript Fundamentals" (2 minggu), 2) Lanjut "React Basics" (3 minggu), 3) "Project-based Learning" (4 minggu). Total estimasi: 9 minggu untuk menjadi job-ready Frontend Developer.';
    }
    
    getHelpResponse() {
        return 'Saya siap membantu! Berikut yang bisa saya lakukan:\n\n• 🎯 Analisis skill gap dan rekomendasi kursus\n• 📈 Prediksi jalur karir berdasarkan market demand\n• 🗺️ Buat learning path yang dipersonalisasi\n• 💼 Info tentang job placement dan salary benchmark\n\nApa yang ingin Anda ketahui lebih lanjut?';
    }
    
    addMessage(content, sender) {
        const chatMessages = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'mb-4 message-animation';
        
        const timestamp = new Date().toLocaleTimeString('id-ID', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        if (sender === 'user') {
            messageDiv.innerHTML = this.getUserMessageHTML(content, timestamp);
        } else {
            messageDiv.innerHTML = this.getAIMessageHTML(content, timestamp);
        }
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        // Store in history
        this.messageHistory.push({ content, sender, timestamp });
        
        // Save to localStorage for persistence
        this.saveMessageHistory();
    }
    
    getUserMessageHTML(content, timestamp) {
        return `
            <div class="flex items-start space-x-3 justify-end">
                <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl rounded-tr-md p-3 shadow-sm max-w-xs">
                    <p class="text-sm">${this.escapeHtml(content)}</p>
                </div>
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
            </div>
            <span class="text-xs text-gray-500 flex justify-end mr-11">${timestamp}</span>
        `;
    }
    
    getAIMessageHTML(content, timestamp) {
        // Convert newlines to <br> for better formatting
        const formattedContent = content.replace(/\n/g, '<br>');
        
        return `
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm max-w-xs">
                    <p class="text-gray-800 text-sm">${formattedContent}</p>
                </div>
            </div>
            <span class="text-xs text-gray-500 ml-11">${timestamp}</span>
        `;
    }
    
    showTypingIndicator() {
        const chatMessages = document.getElementById('chat-messages');
        const typingDiv = document.createElement('div');
        typingDiv.id = 'typing-indicator';
        typingDiv.className = 'mb-4';
        typingDiv.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm">
                    <div class="typing-indicator flex space-x-1">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        `;
        
        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }
    
    showNotification(count = 1) {
        const notification = document.getElementById('chat-notification');
        const notificationCount = document.getElementById('notification-count');
        
        if (notification && notificationCount) {
            notificationCount.textContent = count;
            notification.classList.remove('hidden');
        }
    }
    
    hideNotification() {
        const notification = document.getElementById('chat-notification');
        if (notification) {
            notification.classList.add('hidden');
        }
    }
    
    loadUserProfile() {
        // TODO: Load user profile from backend
        // This will be used for personalized recommendations
        this.userProfile = {
            id: null,
            skills: [],
            interests: [],
            career_goal: null,
            experience_level: 'beginner'
        };
    }
    
    initializeWelcomeMessage() {
        // Add a welcome message if no previous conversation
        if (this.messageHistory.length === 0) {
            // The welcome message is already in the HTML template
            // This method can be used to customize it based on user profile
        }
    }
    
    getConversationContext() {
        // Return last 5 messages for context
        return this.messageHistory.slice(-5);
    }
    
    saveMessageHistory() {
        try {
            localStorage.setItem('ngambiskuy_chat_history', JSON.stringify(this.messageHistory));
        } catch (error) {
            console.warn('Could not save chat history to localStorage:', error);
        }
    }
    
    loadMessageHistory() {
        try {
            const saved = localStorage.getItem('ngambiskuy_chat_history');
            if (saved) {
                this.messageHistory = JSON.parse(saved);
                // TODO: Restore messages to UI if needed
            }
        } catch (error) {
            console.warn('Could not load chat history from localStorage:', error);
        }
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize AI Chat when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('ai-chat-container')) {
        window.ngambiskuyAIChat = new NgambiskuyAIChat();
    }
});

// Export for potential module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NgambiskuyAIChat;
}
