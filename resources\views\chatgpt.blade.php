<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ngambiskuy - Learn Tech Skills</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
</head>
<body class="bg-gray-50 text-gray-900">
    
    <nav class="bg-white shadow-md p-4 flex justify-between items-center fixed w-full top-0 z-50 px-10">
        <a href="#" class="text-3xl font-bold text-blue-600">Ngambiskuy</a>
        <div class="hidden md:flex items-center space-x-6">
            <a href="#" class="hover:text-blue-500">Courses</a>
            <a href="#" class="hover:text-blue-500">About</a>
            <a href="#" class="hover:text-blue-500">Contact</a>
            <a href="#" class="bg-blue-500 text-white px-6 py-2 rounded-lg shadow-md hover:bg-blue-600 flex items-center justify-center">Login</a>
        </div>
    </nav>
    
    <header class="text-center py-24 bg-gradient-to-r from-blue-600 to-indigo-600 text-white" id="hero">
        <h1 class="text-5xl font-extrabold animate-fade-in">Learn, Grow, and Achieve with Ngambiskuy</h1>
        <p class="mt-3 text-lg opacity-80">Access free and premium courses in Data Science, Web Development, Game Dev, and more!</p>
        <a href="#" class="mt-6 inline-block bg-white text-blue-600 px-8 py-4 rounded-full font-semibold shadow-lg hover:scale-105 transform transition-all duration-300">Explore Courses</a>
    </header>
    
    <section class="py-16 px-6">
        <h2 class="text-4xl font-bold text-center mb-6">Our Main Categories</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-white p-8 shadow-xl rounded-lg text-center hover:shadow-2xl transition-transform transform hover:scale-105">
                <h3 class="text-2xl font-semibold">Data Science</h3>
                <p class="mt-3 text-gray-600">Learn AI, Machine Learning, and Data Analytics.</p>
            </div>
            <div class="bg-white p-8 shadow-xl rounded-lg text-center hover:shadow-2xl transition-transform transform hover:scale-105">
                <h3 class="text-2xl font-semibold">Web Development</h3>
                <p class="mt-3 text-gray-600">Master Frontend & Backend technologies.</p>
            </div>
            <div class="bg-white p-8 shadow-xl rounded-lg text-center hover:shadow-2xl transition-transform transform hover:scale-105">
                <h3 class="text-2xl font-semibold">Game Development</h3>
                <p class="mt-3 text-gray-600">Create immersive games with Unity & Unreal Engine.</p>
            </div>
        </div>
    </section>
    
    <section class="py-16 px-6 bg-gray-100">
        <h2 class="text-4xl font-bold text-center mb-6">Top Free Courses</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-white p-8 shadow-xl rounded-lg hover:shadow-2xl transform hover:scale-105 transition-all">
                <h3 class="text-2xl font-semibold">Introduction to Python</h3>
                <p class="mt-3 text-gray-600">Learn the basics of Python programming with hands-on projects.</p>
                <a href="#" class="mt-4 inline-block text-blue-600 font-bold">View Course</a>
            </div>
            <div class="bg-white p-8 shadow-xl rounded-lg hover:shadow-2xl transform hover:scale-105 transition-all">
                <h3 class="text-2xl font-semibold">HTML & CSS for Beginners</h3>
                <p class="mt-3 text-gray-600">Start your web development journey with essential HTML & CSS skills.</p>
                <a href="#" class="mt-4 inline-block text-blue-600 font-bold">View Course</a>
            </div>
            <div class="bg-white p-8 shadow-xl rounded-lg hover:shadow-2xl transform hover:scale-105 transition-all">
                <h3 class="text-2xl font-semibold">Unity Game Development</h3>
                <p class="mt-3 text-gray-600">Learn how to create engaging games using Unity and C#.</p>
                <a href="#" class="mt-4 inline-block text-blue-600 font-bold">View Course</a>
            </div>
        </div>
    </section>
    
    <footer class="bg-blue-600 text-white text-center py-8 mt-12">
        <p class="text-lg">&copy; 2024 Ngambiskuy. All Rights Reserved.</p>
        <div class="mt-4 flex justify-center space-x-6">
            <a href="#" class="hover:text-gray-300">Privacy Policy</a>
            <a href="#" class="hover:text-gray-300">Terms of Service</a>
        </div>
    </footer>
    
    <script>
        gsap.from("#hero", {opacity: 0, y: 30, duration: 1, ease: "power2.out"});
    </script>
</body>
</html>
