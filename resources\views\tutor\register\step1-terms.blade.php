@extends('layouts.app')

@section('title', 'Daftar Sebagai <PERSON>tor - Syarat dan <PERSON>')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Progress Steps -->
        <div class="mb-8">
            <div class="flex items-center justify-center">
                <div class="flex items-center space-x-4">
                    <!-- Step 1 - Active -->
                    <div class="flex items-center">
                        <div class="flex items-center justify-center w-10 h-10 bg-primary text-white rounded-full font-semibold">
                            1
                        </div>
                        <span class="ml-2 text-sm font-medium text-primary">Syarat & Ketentuan</span>
                    </div>

                    <!-- Connector -->
                    <div class="w-16 h-1 bg-gray-300"></div>

                    <!-- Step 2 - Inactive -->
                    <div class="flex items-center">
                        <div class="flex items-center justify-center w-10 h-10 bg-gray-300 text-gray-600 rounded-full font-semibold">
                            2
                        </div>
                        <span class="ml-2 text-sm font-medium text-gray-500">Buat Profil</span>
                    </div>

                    <!-- Connector -->
                    <div class="w-16 h-1 bg-gray-300"></div>

                    <!-- Step 3 - Inactive -->
                    <div class="flex items-center">
                        <div class="flex items-center justify-center w-10 h-10 bg-gray-300 text-gray-600 rounded-full font-semibold">
                            3
                        </div>
                        <span class="ml-2 text-sm font-medium text-gray-500">Review & Submit</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="bg-white rounded-lg shadow-sm border p-8">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Bergabung Sebagai Tutor</h1>
                <p class="text-gray-600">Baca dan setujui syarat dan ketentuan untuk melanjutkan pendaftaran</p>
            </div>

            <form action="{{ route('tutor.register.terms.process') }}" method="POST" class="space-y-6">
                @csrf

                <!-- Terms and Conditions -->
                <div class="bg-gray-50 rounded-lg p-6 max-h-96 overflow-y-auto">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Persyaratan dan Ketentuan</h2>

                    <div class="prose prose-sm text-gray-700 space-y-4">
                        <h3 class="font-semibold text-blue-600">1. Pengenalan</h3>
                        <p>Selamat datang di Ngambiskuy. Syarat dan ketentuan ini bertujuan untuk mengatur hubungan antara Ngambiskuy dan Anda sebagai pengajar. Dengan mendaftar sebagai pengajar, Anda setuju untuk mematuhi syarat dan ketentuan yang ditetapkan di bawah ini.</p>

                        <h3 class="font-semibold text-blue-600">2. Kualifikasi Pengajar</h3>
                        <p>Pengajar harus memenuhi kriteria berikut:</p>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Memiliki kualifikasi pendidikan atau sertifikat keahlian yang relevan.</li>
                            <li>Menyediakan portofolio atau resume yang menunjukkan pengalaman mengajar atau keahlian di bidang terkait.</li>
                            <li>Berkomitmen untuk mematuhi standar etika dan profesionalisme yang tinggi dalam menyampaikan materi pembelajaran.</li>
                        </ul>

                        <h3 class="font-semibold text-blue-600">3. Materi Khusus</h3>
                        <p>Pengajar bertanggung jawab untuk menyediakan materi khusus yang:</p>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Tidak melanggar hak cipta atau hak kekayaan intelektual pihak lain.</li>
                            <li>Bebas dari konten yang mengandung ujaran kebencian, diskriminasi, atau material yang tidak pantas.</li>
                            <li>Memenuhi standar kualitas Ngambiskuy dalam hal edukasi dan penyampaian materi.</li>
                        </ul>

                        <h3 class="font-semibold text-blue-600">4. Hak dan Kewajiban</h3>

                        <h4 class="font-medium">• Persetujuan dan Pengesahan Materi Kursus</h4>
                        <p>Ngambiskuy berhak untuk meninjau dan menyetujui materi kursus sebelum dipublikasikan untuk memastikan bahwa materi tersebut memenuhi standar kualitas yang ditetapkan oleh Ngambiskuy.</p>

                        <h4 class="font-medium">• Kompensasi</h4>
                        <p>Pengajar berhak menerima kompensasi sesuai dengan ketentuan yang telah disepakati. Struktur kompensasi adalah sebagai berikut:</p>
                        <ul class="list-disc pl-5 space-y-1">
                            <li><strong>Tanpa Kode Referral:</strong> Pengajar akan menerima 60% dari total pendapatan kursus setelah dikurangi biaya sistem dan potongan lainnya yang berlaku.</li>
                            <li><strong>Dengan Kode Referral:</strong> Pengajar yang menggunakan kode referral pada saat pendaftaran akan menerima 80% dari total pendapatan kursus, setelah dikurangi biaya sistem dan potongan lainnya. Penggunaan kode referral harus sesuai dengan pedoman dan ketentuan yang telah ditetapkan oleh Ngambiskuy.</li>
                        </ul>

                        <h4 class="font-medium">• Kewajiban Pengajar</h4>
                        <p>Pengajar wajib mematuhi jadwal dan komitmen yang telah disepakati dalam menyelenggarakan kursus dan menjaga standar profesionalisme serta etika pengajaran.</p>

                        <h4 class="font-medium">• Penyelesaian Konflik</h4>
                        <p>Setiap permasalahan yang timbul akan diselesaikan melalui mediasi dan diskusi antara pengajar dan manajemen Ngambiskuy.</p>
                    </div>
                </div>

                <!-- Privacy Policy -->
                <div class="bg-gray-50 rounded-lg p-6 max-h-96 overflow-y-auto">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Kebijakan Privasi</h2>

                    <div class="prose prose-sm text-gray-700 space-y-4">
                        <h3 class="font-semibold text-blue-600">1. Pengumpulan Informasi</h3>
                        <p>Ngambiskuy mengumpulkan informasi pribadi pengajar, seperti nama lengkap, alamat email, nomor telepon, dan informasi rekening bank untuk keperluan administrasi dan komunikasi terkait operasional kursus.</p>

                        <h3 class="font-semibold text-blue-600">2. Penggunaan Informasi</h3>
                        <p>Informasi yang dikumpulkan akan digunakan untuk:</p>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Proses pembayaran dan transfer kompensasi.</li>
                            <li>Komunikasi terkait operasional kursus dan platform.</li>
                            <li>Promosi dan pengumuman yang relevan dari Ngambiskuy.</li>
                        </ul>

                        <h3 class="font-semibold text-blue-600">3. Perlindungan Informasi</h3>
                        <p>Ngambiskuy berkomitmen untuk melindungi privasi dan keamanan data pengajar. Kami mengimplementasikan berbagai langkah keamanan untuk melindungi informasi dari akses tidak sah atau pengungkapan tanpa izin.</p>

                        <h3 class="font-semibold text-blue-600">4. Perlindungan Informasi</h3>
                        <p>Ngambiskuy tidak akan membagikan informasi pribadi pengajar dengan pihak ketiga tanpa persetujuan, kecuali jika diwajibkan oleh hukum.</p>

                        <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <p class="text-sm text-blue-800">
                                <strong>Catatan:</strong> Syarat dan ketentuan serta kebijakan privasi ini dapat diperbarui dari waktu ke waktu. Pengajar akan diberitahu mengenai perubahan signifikan melalui email atau pengumuman di platform.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Agreement Checkboxes -->
                <div class="space-y-4">
                    <div class="flex items-start">
                        <input type="checkbox"
                               id="terms_agreed"
                               name="terms_agreed"
                               value="1"
                               class="mt-1 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                               {{ old('terms_agreed') ? 'checked' : '' }}>
                        <label for="terms_agreed" class="ml-3 text-sm text-gray-700">
                            Saya setuju dengan <strong>Syarat dan Ketentuan Pengajar</strong> yang telah ditetapkan oleh Ngambiskuy.
                        </label>
                    </div>
                    @error('terms_agreed')
                        <p class="text-red-500 text-sm">{{ $message }}</p>
                    @enderror

                    <div class="flex items-start">
                        <input type="checkbox"
                               id="privacy_agreed"
                               name="privacy_agreed"
                               value="1"
                               class="mt-1 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                               {{ old('privacy_agreed') ? 'checked' : '' }}>
                        <label for="privacy_agreed" class="ml-3 text-sm text-gray-700">
                            Saya telah membaca, memahami, dan menyetujui semua ketentuan yang berlaku di <strong>Kebijakan Privasi</strong> yang telah ditetapkan oleh Ngambiskuy.
                        </label>
                    </div>
                    @error('privacy_agreed')
                        <p class="text-red-500 text-sm">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-between pt-6">
                    <a href="{{ route('home') }}"
                       class="btn btn-outline px-6 py-3">
                        Kembali
                    </a>

                    <button type="submit"
                            class="btn btn-primary px-8 py-3">
                        Lanjutkan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
