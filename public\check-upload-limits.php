<?php
/**
 * PHP Upload Limits Checker
 * Access this file via: http://your-domain.com/check-upload-limits.php
 */

// Function to convert PHP ini values to bytes
function convertToBytes($value) {
    $value = trim($value);
    $last = strtolower($value[strlen($value)-1]);
    $value = (int) $value;

    switch($last) {
        case 'g':
            $value *= 1024;
        case 'm':
            $value *= 1024;
        case 'k':
            $value *= 1024;
    }

    return $value;
}

// Function to format bytes to human readable
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');

    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }

    return round($bytes, $precision) . ' ' . $units[$i];
}

// Get current PHP settings
$uploadMaxFilesize = ini_get('upload_max_filesize');
$postMaxSize = ini_get('post_max_size');
$maxExecutionTime = ini_get('max_execution_time');
$maxInputTime = ini_get('max_input_time');
$memoryLimit = ini_get('memory_limit');
$fileUploads = ini_get('file_uploads');
$maxFileUploads = ini_get('max_file_uploads');

// Convert to bytes for comparison
$uploadMaxFilesizeBytes = convertToBytes($uploadMaxFilesize);
$postMaxSizeBytes = convertToBytes($postMaxSize);
$memoryLimitBytes = convertToBytes($memoryLimit);

// Determine the effective upload limit
$effectiveLimit = min($uploadMaxFilesizeBytes, $postMaxSizeBytes);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP Upload Limits Checker</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .recommendation { background: #e7f3ff; padding: 20px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>PHP Upload Limits Checker</h1>

        <?php if ($effectiveLimit >= 512 * 1024 * 1024): ?>
            <div class="status success">
                ✅ Upload limits are configured correctly for 512MB files!
            </div>
        <?php elseif ($effectiveLimit >= 100 * 1024 * 1024): ?>
            <div class="status warning">
                ⚠️ Upload limits allow files up to <?= formatBytes($effectiveLimit) ?>, but 512MB is recommended for video uploads.
            </div>
        <?php else: ?>
            <div class="status error">
                ❌ Upload limits are too low (<?= formatBytes($effectiveLimit) ?>). Video uploads will fail!
            </div>
        <?php endif; ?>

        <h2>Current PHP Configuration</h2>
        <table>
            <tr>
                <th>Setting</th>
                <th>Current Value</th>
                <th>Recommended</th>
                <th>Status</th>
            </tr>
            <tr>
                <td>upload_max_filesize</td>
                <td><?= $uploadMaxFilesize ?></td>
                <td>512M</td>
                <td><?= $uploadMaxFilesizeBytes >= 512 * 1024 * 1024 ? '✅' : '❌' ?></td>
            </tr>
            <tr>
                <td>post_max_size</td>
                <td><?= $postMaxSize ?></td>
                <td>512M</td>
                <td><?= $postMaxSizeBytes >= 512 * 1024 * 1024 ? '✅' : '❌' ?></td>
            </tr>
            <tr>
                <td>max_execution_time</td>
                <td><?= $maxExecutionTime ?> seconds</td>
                <td>300 seconds</td>
                <td><?= $maxExecutionTime >= 300 ? '✅' : '❌' ?></td>
            </tr>
            <tr>
                <td>max_input_time</td>
                <td><?= $maxInputTime ?> seconds</td>
                <td>300 seconds</td>
                <td><?= $maxInputTime >= 300 ? '✅' : '❌' ?></td>
            </tr>
            <tr>
                <td>memory_limit</td>
                <td><?= $memoryLimit ?></td>
                <td>512M</td>
                <td><?= $memoryLimitBytes >= 512 * 1024 * 1024 ? '✅' : '❌' ?></td>
            </tr>
            <tr>
                <td>file_uploads</td>
                <td><?= $fileUploads ? 'On' : 'Off' ?></td>
                <td>On</td>
                <td><?= $fileUploads ? '✅' : '❌' ?></td>
            </tr>
            <tr>
                <td>max_file_uploads</td>
                <td><?= $maxFileUploads ?></td>
                <td>20</td>
                <td><?= $maxFileUploads >= 20 ? '✅' : '❌' ?></td>
            </tr>
        </table>

        <div class="recommendation">
            <h3>How to Fix Upload Limits</h3>

            <p><strong>Quick Test:</strong> Try uploading a video smaller than <?= formatBytes($effectiveLimit) ?> first.</p>

            <p><strong>Method 1: Restart your development server</strong></p>
            <p>Stop your Laravel server (Ctrl+C) and restart it: <code>php artisan serve</code></p>

            <p><strong>Method 2: Check your development environment</strong></p>
            <ul>
                <li><strong>XAMPP/WAMP:</strong> Edit php.ini in your XAMPP/WAMP installation</li>
                <li><strong>Laravel Sail:</strong> The .htaccess should work automatically</li>
                <li><strong>Built-in PHP server:</strong> Use the php.ini file in your project root</li>
            </ul>

            <p><strong>Method 3: Files created for you:</strong></p>
            <ul>
                <li><code>.htaccess</code> - For Apache servers</li>
                <li><code>php.ini</code> - Custom PHP configuration</li>
                <li><code>.user.ini</code> - For some hosting environments</li>
                <li><code>nginx-upload-config.conf</code> - For Nginx servers</li>
            </ul>

            <p><strong>Method 4: Use smaller files for testing</strong></p>
            <p>The app is now set to 20MB limit for testing. Try with a small video first.</p>

            <p><strong>After making changes:</strong></p>
            <ul>
                <li>Restart your development server</li>
                <li>Clear browser cache</li>
                <li>Refresh this page to verify changes</li>
                <li>Test with a small video file (under 10MB)</li>
            </ul>
        </div>

        <p><small>Effective upload limit: <strong><?= formatBytes($effectiveLimit) ?></strong></small></p>
    </div>
</body>
</html>
