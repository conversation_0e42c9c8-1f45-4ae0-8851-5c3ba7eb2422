@echo off
echo Starting Laravel development server with increased upload limits...
echo.
echo IMPORTANT: You need to manually edit your XAMPP php.ini file:
echo Location: C:\xampp\php\php.ini
echo.
echo Change these values:
echo upload_max_filesize = 150M
echo post_max_size = 150M
echo max_execution_time = 600
echo max_input_time = 600
echo memory_limit = 512M
echo.
echo After editing, restart XAMPP and use: php artisan serve
echo.
echo For now, starting with current settings...
echo Server will be available at: http://127.0.0.1:8000
echo Press Ctrl+C to stop the server
echo.

REM Start PHP development server
php artisan serve --host=127.0.0.1 --port=8000

pause
