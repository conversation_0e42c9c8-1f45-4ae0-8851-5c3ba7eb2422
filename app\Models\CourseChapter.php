<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class CourseChapter extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'course_id',
        'title',
        'slug',
        'description',
        'sort_order',
        'duration_minutes',
        'is_published',
        'is_free',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_published' => 'boolean',
        'is_free' => 'boolean',
        'sort_order' => 'integer',
        'duration_minutes' => 'integer',
    ];

    /**
     * Get the course that owns the chapter.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the lessons for the chapter.
     */
    public function lessons(): HasMany
    {
        return $this->hasMany(CourseLesson::class, 'chapter_id')->orderBy('sort_order');
    }

    /**
     * Get published lessons for the chapter.
     */
    public function publishedLessons(): HasMany
    {
        return $this->lessons()->where('is_published', true);
    }

    /**
     * Get published chapters.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($chapter) {
            if (empty($chapter->slug)) {
                $chapter->slug = Str::slug($chapter->title);
            }
        });

        static::updating(function ($chapter) {
            if ($chapter->isDirty('title') && empty($chapter->slug)) {
                $chapter->slug = Str::slug($chapter->title);
            }
        });
    }
}
