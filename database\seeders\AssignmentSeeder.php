<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\CourseLesson;
use App\Models\LessonAssignment;
use Carbon\Carbon;

class AssignmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some assignment-type lessons
        $assignmentLessons = CourseLesson::where('type', 'assignment')->take(3)->get();

        foreach ($assignmentLessons as $lesson) {
            LessonAssignment::create([
                'lesson_id' => $lesson->id,
                'title' => 'Tugas: ' . $lesson->title,
                'description' => $this->getAssignmentDescription($lesson->title),
                'requirements' => $this->getAssignmentRequirements(),
                'allowed_file_types' => ['pdf', 'doc', 'docx', 'txt', 'zip'],
                'max_file_size' => 10, // 10MB
                'max_files' => 3,
                'due_date' => Carbon::now()->addDays(7), // Due in 7 days
                'max_points' => 100,
                'allow_late_submission' => true,
                'late_penalty_percent' => 10, // 10% penalty per day late
                'is_published' => true,
            ]);
        }
    }

    private function getAssignmentDescription(string $lessonTitle): string
    {
        return "
        <h3>Deskripsi Tugas</h3>
        <p>Dalam tugas ini, Anda diminta untuk menerapkan konsep-konsep yang telah dipelajari dalam materi <strong>{$lessonTitle}</strong>.</p>
        
        <h4>Tujuan Pembelajaran:</h4>
        <ul>
            <li>Memahami dan menerapkan konsep yang telah dipelajari</li>
            <li>Mengembangkan kemampuan problem solving</li>
            <li>Melatih kemampuan dokumentasi dan presentasi</li>
        </ul>
        
        <h4>Instruksi:</h4>
        <ol>
            <li>Baca dengan teliti semua requirements yang diberikan</li>
            <li>Kerjakan tugas sesuai dengan panduan yang telah disediakan</li>
            <li>Dokumentasikan proses pengerjaan Anda</li>
            <li>Submit hasil pekerjaan sebelum deadline</li>
        </ol>
        
        <p><strong>Catatan:</strong> Pastikan semua file yang disubmit dapat dibuka dan dibaca dengan baik.</p>
        ";
    }

    private function getAssignmentRequirements(): string
    {
        return "
        <h4>Requirements:</h4>
        <ul>
            <li>Kode program harus bersih dan mudah dibaca</li>
            <li>Sertakan komentar yang menjelaskan logika program</li>
            <li>Buat dokumentasi singkat tentang cara menjalankan program</li>
            <li>Test program Anda sebelum submit</li>
            <li>Sertakan screenshot hasil running program</li>
        </ul>
        
        <h4>Format Submission:</h4>
        <ul>
            <li>File utama: [NamaAnda]_[NamaTugas].zip</li>
            <li>Dokumentasi: README.md atau README.txt</li>
            <li>Source code dengan ekstensi yang sesuai</li>
            <li>Screenshot dalam folder 'screenshots'</li>
        </ul>
        
        <h4>Kriteria Penilaian:</h4>
        <ul>
            <li>Fungsionalitas (40%)</li>
            <li>Kualitas kode (30%)</li>
            <li>Dokumentasi (20%)</li>
            <li>Kreativitas (10%)</li>
        </ul>
        ";
    }
}
