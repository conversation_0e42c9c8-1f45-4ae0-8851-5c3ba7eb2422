@extends('layouts.tutor')

@section('title', '<PERSON><PERSON> Materi - ' . $chapter->title)

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Tambah Materi Baru</h1>
                <p class="text-gray-600 mt-1">{{ $course->title }} - {{ $chapter->title }}</p>
                <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                        {{ $course->category->name }}
                    </span>
                    <span class="flex items-center">
                        @if($course->is_free)
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">GRATIS</span>
                        @else
                            <span class="bg-primary text-white text-xs px-2 py-1 rounded">{{ $course->formatted_price }}</span>
                        @endif
                    </span>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('tutor.curriculum.index', $course) }}" class="btn btn-outline">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Kembali ke Kurikulum
                </a>
            </div>
        </div>
    </div>

    <!-- Material Creation Form -->
    <div class="bg-white rounded-lg shadow-sm">
        <form action="{{ route('tutor.curriculum.store-lesson', [$course, $chapter]) }}" method="POST" enctype="multipart/form-data" id="materialForm">
            @csrf
            
            <div class="p-6 space-y-6">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                            Judul Materi <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="title" name="title" required value="{{ old('title') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                               placeholder="Contoh: 1.1 Pengenalan Konsep Dasar">
                        @error('title')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="duration_minutes" class="block text-sm font-medium text-gray-700 mb-2">
                            Durasi (menit) <span class="text-red-500">*</span>
                        </label>
                        <input type="number" id="duration_minutes" name="duration_minutes" required min="1" max="300" value="{{ old('duration_minutes', 15) }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                               placeholder="15">
                        @error('duration_minutes')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi</label>
                    <textarea id="description" name="description" rows="3"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 resize-none"
                              placeholder="Deskripsi singkat tentang materi ini...">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Material Type Selection -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
                        Tipe Materi <span class="text-red-500">*</span>
                    </label>
                    <select id="type" name="type" required onchange="toggleMaterialFields()"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        <option value="">Pilih tipe materi</option>
                        <option value="video" {{ old('type') == 'video' ? 'selected' : '' }}>Video</option>
                        <option value="text" {{ old('type') == 'text' ? 'selected' : '' }}>Teks/Artikel</option>
                        <option value="quiz" {{ old('type') == 'quiz' ? 'selected' : '' }}>Kuis</option>
                        <option value="assignment" {{ old('type') == 'assignment' ? 'selected' : '' }}>Tugas</option>
                    </select>
                    @error('type')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Dynamic Content Areas -->
            <div class="border-t border-gray-200">
                <!-- Video Content -->
                <div id="video_content" class="hidden p-6 space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Konten Video</h3>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-3">Sumber Video</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input type="radio" name="video_source" value="url" class="h-4 w-4 text-primary focus:ring-primary border-gray-300" onchange="toggleVideoSource()">
                                <div class="ml-3">
                                    <span class="text-sm font-medium text-gray-700">URL Video</span>
                                    <p class="text-xs text-gray-500">YouTube, Vimeo, dll</p>
                                </div>
                            </label>
                            <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input type="radio" name="video_source" value="upload" class="h-4 w-4 text-primary focus:ring-primary border-gray-300" onchange="toggleVideoSource()">
                                <div class="ml-3">
                                    <span class="text-sm font-medium text-gray-700">Upload Video</span>
                                    <p class="text-xs text-gray-500">File MP4, MOV, AVI</p>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- URL Input -->
                    <div id="video_url_input" class="hidden">
                        <label for="video_url" class="block text-sm font-medium text-gray-700 mb-2">URL Video</label>
                        <input type="url" id="video_url" name="video_url" value="{{ old('video_url') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                               placeholder="https://www.youtube.com/watch?v=...">
                        <p class="text-xs text-gray-500 mt-1">Mendukung YouTube, Vimeo, dan platform video lainnya</p>
                    </div>

                    <!-- File Upload -->
                    <div id="video_file_input" class="hidden">
                        <label for="video_file" class="block text-sm font-medium text-gray-700 mb-2">Upload Video</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary transition-colors">
                            <input type="file" id="video_file" name="video_file" accept="video/*" class="hidden" onchange="handleVideoFileSelect(this)">
                            <input type="hidden" id="uploaded_video_path" name="uploaded_video_path" value="{{ old('uploaded_video_path') }}">

                            <!-- Upload Area -->
                            <div id="video_upload_area" onclick="document.getElementById('video_file').click()" class="cursor-pointer">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                <p class="text-sm text-gray-600 mb-2">Klik untuk upload video atau drag & drop</p>
                                <p class="text-xs text-gray-500">MP4, MOV, AVI (Max: 100MB)</p>
                            </div>

                            <!-- Upload Progress -->
                            <div id="video_upload_progress" class="hidden">
                                <div class="flex items-center justify-center space-x-3 mb-4">
                                    <svg class="w-6 h-6 text-blue-500 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    <span class="text-sm font-medium text-gray-700">Mengupload video...</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-3 mb-2">
                                    <div id="upload_progress_bar" class="bg-blue-500 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-500">
                                    <span id="upload_progress_text">0%</span>
                                    <span id="upload_speed_text">0 KB/s</span>
                                </div>
                                <button type="button" onclick="cancelVideoUpload()" class="text-xs text-red-600 hover:text-red-800 mt-2">Batalkan Upload</button>
                            </div>

                            <!-- Upload Success -->
                            <div id="video_file_info" class="hidden">
                                <div class="flex items-center justify-center space-x-2 mb-2">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span id="video_file_name" class="text-sm text-gray-700"></span>
                                </div>
                                <div class="text-xs text-gray-500 mb-2">
                                    <span id="video_file_size"></span> • Upload berhasil
                                </div>
                                <button type="button" onclick="clearVideoFile()" class="text-xs text-red-600 hover:text-red-800">Hapus file</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Text/Article Content -->
                <div id="text_content" class="hidden p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Konten Artikel</h3>
                    
                    <div>
                        <label for="content" class="block text-sm font-medium text-gray-700 mb-2">Konten</label>
                        <textarea id="content" name="content" rows="20"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                                  placeholder="Tulis konten artikel di sini...">{{ old('content') }}</textarea>
                        @error('content')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Quiz Content -->
                <div id="quiz_content" class="hidden p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Pengaturan Kuis</h3>
                    <div id="quiz_builder">
                        <!-- Quiz builder will be loaded here via JavaScript -->
                        <div class="text-center py-8">
                            <p class="text-gray-500">Kuis builder akan dimuat di sini...</p>
                        </div>
                    </div>
                </div>

                <!-- Assignment Content -->
                <div id="assignment_content" class="hidden p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Pengaturan Tugas</h3>
                    <div id="assignment_builder">
                        <!-- Assignment builder will be loaded here via JavaScript -->
                        <div class="text-center py-8">
                            <p class="text-gray-500">Assignment builder akan dimuat di sini...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings -->
            <div class="border-t border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Pengaturan</h3>
                
                @if(!$course->is_free)
                    <div class="bg-blue-50 p-4 rounded-lg mb-4">
                        <label class="flex items-start">
                            <input type="checkbox" name="is_preview" value="1" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1" {{ old('is_preview') ? 'checked' : '' }}>
                            <div class="ml-3">
                                <span class="text-sm font-medium text-gray-700">Preview Materi</span>
                                <p class="text-xs text-gray-500 mt-1">Dapat dilihat sebelum membeli kursus (cocok untuk menarik minat calon siswa)</p>
                            </div>
                        </label>
                    </div>
                @else
                    <div class="bg-green-50 p-4 rounded-lg mb-4">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="ml-3">
                                <span class="text-sm font-medium text-gray-700">Kursus Gratis</span>
                                <p class="text-xs text-gray-500 mt-1">Semua materi dalam kursus ini dapat diakses secara gratis</p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Form Actions -->
            <div class="border-t border-gray-200 px-6 py-4 bg-gray-50 rounded-b-lg">
                <div class="flex justify-end space-x-3">
                    <a href="{{ route('tutor.curriculum.index', $course) }}" 
                       class="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors duration-200">
                        Batal
                    </a>
                    <button type="submit" id="submit_btn"
                            class="px-6 py-2 text-sm font-medium text-white bg-primary hover:bg-primary-dark rounded-lg transition-colors duration-200 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span id="submit_text">Simpan Materi</span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('styles')
<!-- TinyMCE CSS -->
<style>
    .tox-tinymce {
        border-radius: 0.5rem !important;
        border-color: #d1d5db !important;
    }
    .tox-editor-header {
        border-radius: 0.5rem 0.5rem 0 0 !important;
    }
    .tox-edit-area {
        border-radius: 0 0 0.5rem 0.5rem !important;
    }
</style>
@endpush

@push('scripts')
<!-- TinyMCE -->
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>

<script>
// Initialize TinyMCE
function initializeTinyMCE() {
    tinymce.init({
        selector: '#content',
        height: 500,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | ' +
                'bold italic forecolor | alignleft aligncenter ' +
                'alignright alignjustify | bullist numlist outdent indent | ' +
                'removeformat | help',
        content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; }',
        setup: function (editor) {
            editor.on('change', function () {
                editor.save();
            });
        }
    });
}

// Toggle material type fields
function toggleMaterialFields() {
    const type = document.getElementById('type').value;
    
    // Hide all content areas
    document.getElementById('video_content').classList.add('hidden');
    document.getElementById('text_content').classList.add('hidden');
    document.getElementById('quiz_content').classList.add('hidden');
    document.getElementById('assignment_content').classList.add('hidden');
    
    // Show relevant content area
    if (type === 'video') {
        document.getElementById('video_content').classList.remove('hidden');
    } else if (type === 'text') {
        document.getElementById('text_content').classList.remove('hidden');
        // Initialize TinyMCE when text area is shown
        setTimeout(initializeTinyMCE, 100);
    } else if (type === 'quiz') {
        document.getElementById('quiz_content').classList.remove('hidden');
        loadQuizBuilder();
    } else if (type === 'assignment') {
        document.getElementById('assignment_content').classList.remove('hidden');
        loadAssignmentBuilder();
    }
}

// Toggle video source
function toggleVideoSource() {
    const urlRadio = document.querySelector('input[name="video_source"][value="url"]');
    const uploadRadio = document.querySelector('input[name="video_source"][value="upload"]');
    const urlInput = document.getElementById('video_url_input');
    const fileInput = document.getElementById('video_file_input');

    if (urlRadio.checked) {
        urlInput.classList.remove('hidden');
        fileInput.classList.add('hidden');
    } else if (uploadRadio.checked) {
        urlInput.classList.add('hidden');
        fileInput.classList.remove('hidden');
    }
}

// Load quiz builder
function loadQuizBuilder() {
    document.getElementById('quiz_builder').innerHTML = `
        <div class="space-y-6">
            <div class="bg-yellow-50 p-4 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-2">Quiz Builder</h4>
                <p class="text-sm text-gray-600">Fitur quiz builder akan segera tersedia. Untuk sementara, Anda dapat membuat kuis sederhana.</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Instruksi Kuis</label>
                <textarea name="quiz_instructions" rows="3" 
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="Masukkan instruksi untuk kuis ini..."></textarea>
            </div>
        </div>
    `;
}

// Load assignment builder
function loadAssignmentBuilder() {
    document.getElementById('assignment_builder').innerHTML = `
        <div class="space-y-6">
            <div class="bg-purple-50 p-4 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-2">Assignment Builder</h4>
                <p class="text-sm text-gray-600">Fitur assignment builder akan segera tersedia. Untuk sementara, Anda dapat membuat tugas sederhana.</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Instruksi Tugas</label>
                <textarea name="assignment_instructions" rows="3" 
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="Masukkan instruksi untuk tugas ini..."></textarea>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Tanggal Deadline</label>
                <input type="datetime-local" name="assignment_deadline" 
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>
        </div>
    `;
}

// Video upload functionality (simplified version)
let currentUpload = null;

function handleVideoFileSelect(input) {
    const file = input.files[0];
    if (file) {
        // Check file size (100MB limit)
        const maxSize = 100 * 1024 * 1024;
        if (file.size > maxSize) {
            alert('File terlalu besar! Maksimal 100MB.');
            input.value = '';
            return;
        }

        // Check file type
        const allowedTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/quicktime'];
        if (!allowedTypes.includes(file.type)) {
            alert('Format file tidak didukung! Gunakan MP4, MOV, atau AVI.');
            input.value = '';
            return;
        }

        // Show file info
        document.getElementById('video_upload_area').classList.add('hidden');
        document.getElementById('video_file_info').classList.remove('hidden');
        document.getElementById('video_file_name').textContent = file.name;
        document.getElementById('video_file_size').textContent = formatFileSize(file.size);
    }
}

function clearVideoFile() {
    document.getElementById('video_file').value = '';
    document.getElementById('uploaded_video_path').value = '';
    document.getElementById('video_upload_area').classList.remove('hidden');
    document.getElementById('video_file_info').classList.add('hidden');
}

function formatFileSize(bytes) {
    if (bytes >= 1024 * 1024) {
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    } else if (bytes >= 1024) {
        return (bytes / 1024).toFixed(1) + ' KB';
    } else {
        return bytes + ' bytes';
    }
}

// Form validation
document.getElementById('materialForm').addEventListener('submit', function(e) {
    const type = document.getElementById('type').value;
    
    if (type === 'video') {
        const videoSource = document.querySelector('input[name="video_source"]:checked');
        const videoUrl = document.getElementById('video_url').value;
        const videoFile = document.getElementById('video_file').files[0];
        
        if (!videoSource) {
            e.preventDefault();
            alert('Pilih sumber video (URL atau Upload).');
            return false;
        }
        
        if (videoSource.value === 'url' && !videoUrl) {
            e.preventDefault();
            alert('Masukkan URL video.');
            return false;
        }
        
        if (videoSource.value === 'upload' && !videoFile) {
            e.preventDefault();
            alert('Upload video terlebih dahulu.');
            return false;
        }
    }
    
    // Disable submit button to prevent double submission
    const submitBtn = document.getElementById('submit_btn');
    const submitText = document.getElementById('submit_text');
    submitBtn.disabled = true;
    submitText.textContent = 'Menyimpan...';
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Check if there's an old type value and show the appropriate content
    const oldType = '{{ old("type") }}';
    if (oldType) {
        document.getElementById('type').value = oldType;
        toggleMaterialFields();
    }
});
</script>
@endpush
