// Ngambiskuy Homepage JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initMobileMenu();
    initCourseTabs();
    initCourseFilters();
    initScrollAnimations();
    initLiveChat();
});

// Mobile Menu Functionality
function initMobileMenu() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mobileMenu = document.querySelector('.mobile-menu');
    
    if (mobileMenuToggle && mobileMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
            
            // Toggle icon
            const icon = this.querySelector('svg');
            if (mobileMenu.classList.contains('hidden')) {
                icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>';
            } else {
                icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
            }
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!mobileMenuToggle.contains(event.target) && !mobileMenu.contains(event.target)) {
                mobileMenu.classList.add('hidden');
                const icon = mobileMenuToggle.querySelector('svg');
                icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>';
            }
        });
    }
}

// Course Tabs Functionality
function initCourseTabs() {
    const courseTabs = document.querySelectorAll('.course-tab');
    
    courseTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            courseTabs.forEach(t => t.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            
            // Filter courses based on active tab
            filterCourses();
        });
    });
}

// Course Filters Functionality
function initCourseFilters() {
    const searchInput = document.getElementById('course-search');
    const categoryFilter = document.getElementById('category-filter');
    const levelFilter = document.getElementById('level-filter');
    
    if (searchInput) {
        searchInput.addEventListener('input', debounce(filterCourses, 300));
    }
    
    if (categoryFilter) {
        categoryFilter.addEventListener('change', filterCourses);
    }
    
    if (levelFilter) {
        levelFilter.addEventListener('change', filterCourses);
    }
}

// Filter courses based on active tab and filters
function filterCourses() {
    const activeTab = document.querySelector('.course-tab.active');
    const searchInput = document.getElementById('course-search');
    const categoryFilter = document.getElementById('category-filter');
    const levelFilter = document.getElementById('level-filter');
    const courseCards = document.querySelectorAll('.course-card');
    
    if (!activeTab) return;
    
    const tabType = activeTab.dataset.tab;
    const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
    const selectedCategory = categoryFilter ? categoryFilter.value : 'all';
    const selectedLevel = levelFilter ? levelFilter.value : 'all';
    
    let visibleCount = 0;
    
    courseCards.forEach(card => {
        const cardType = card.dataset.type;
        const cardCategory = card.dataset.category;
        const cardLevel = card.dataset.level;
        const cardTitle = card.querySelector('h3').textContent.toLowerCase();
        const cardDescription = card.querySelector('p').textContent.toLowerCase();
        const priceElement = card.querySelector('.text-green-600');
        
        let shouldShow = true;
        
        // Filter by tab type
        if (tabType === 'gratis') {
            shouldShow = cardType === 'free' || priceElement !== null;
        } else if (tabType === 'premium') {
            shouldShow = cardType === 'premium';
        } else if (tabType === 'tutorial') {
            shouldShow = cardType === 'tutorial';
        } else if (tabType === 'webinar') {
            shouldShow = cardType === 'webinar';
        }
        
        // Filter by search term
        if (searchTerm && shouldShow) {
            shouldShow = cardTitle.includes(searchTerm) || 
                        cardDescription.includes(searchTerm) ||
                        cardCategory.toLowerCase().includes(searchTerm);
        }
        
        // Filter by category
        if (selectedCategory !== 'all' && shouldShow) {
            shouldShow = cardCategory === selectedCategory;
        }
        
        // Filter by level
        if (selectedLevel !== 'all' && shouldShow) {
            shouldShow = cardLevel === selectedLevel;
        }
        
        // Show/hide card with animation
        if (shouldShow) {
            card.style.display = 'block';
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, visibleCount * 50);
            
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });
    
    // Show "no results" message if no courses are visible
    showNoResultsMessage(visibleCount === 0);
}

// Show/hide no results message
function showNoResultsMessage(show) {
    let noResultsElement = document.getElementById('no-results-message');
    
    if (show && !noResultsElement) {
        const courseGrid = document.getElementById('course-grid');
        noResultsElement = document.createElement('div');
        noResultsElement.id = 'no-results-message';
        noResultsElement.className = 'col-span-full text-center py-12';
        noResultsElement.innerHTML = `
            <div class="text-gray-400 mb-4">
                <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak ada kursus ditemukan</h3>
            <p class="text-gray-600">Coba ubah filter atau kata kunci pencarian Anda</p>
        `;
        courseGrid.appendChild(noResultsElement);
    } else if (!show && noResultsElement) {
        noResultsElement.remove();
    }
}

// Scroll animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.course-card, .testimonial-card, .blog-card');
    animateElements.forEach(el => observer.observe(el));
}

// Simple live chat functionality
function initLiveChat() {
    // Create chat button
    const chatButton = document.createElement('button');
    chatButton.className = 'fixed bottom-6 right-6 w-14 h-14 bg-primary text-white rounded-full shadow-lg hover:bg-primary-dark transition-colors z-50';
    chatButton.innerHTML = `
        <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
    `;
    
    chatButton.addEventListener('click', function() {
        alert('Fitur live chat akan segera hadir! Untuk sementara, Anda dapat menghubungi kami melalui email: <EMAIL>');
    });
    
    document.body.appendChild(chatButton);
}

// Utility function: Debounce
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add fade-in animation CSS
const style = document.createElement('style');
style.textContent = `
    .animate-fade-in {
        animation: fadeIn 0.6s ease-out forwards;
    }
    
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);