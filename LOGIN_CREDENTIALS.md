# Ngambiskuy - Login Credentials

## Test Users for Authentication

The following test users have been created for testing the authentication system:

### 🔑 **Login Credentials**

All users have the same password: `password123`

| Name | Email | Role | Permissions |
|------|-------|------|-------------|
| Super Admin Ngambiskuy | <EMAIL> | Super Admin | Full system access |
| Admin <PERSON> | <EMAIL> | Admin | Admin + Tutor access |
| Sari <PERSON> | <EMAIL> | Tutor | Create/manage courses |
| <PERSON> | <EMAIL> | Tutor | Create/manage courses |
| Test User | <EMAIL> | Student | Enroll in courses |
| Budi <PERSON> | <EMAIL> | Student | Enroll in courses |
| Rina Sari | <EMAIL> | Student | Enroll in courses |
| Doni <PERSON> | <EMAIL> | Student | Enroll in courses |

### 🚀 **How to Test**

1. **Visit the homepage**: http://127.0.0.1:8000
2. **Click "Masuk"** in the top navigation
3. **Use any of the credentials above** to login
4. **After login**, you'll be redirected to the dashboard

### 📱 **Available Routes**

#### Public Routes
- **Homepage**: `/` - Main landing page
- **Login**: `/login` - Login form
- **Register**: `/register` - Registration form

#### Student Dashboard (All authenticated users)
- **Dashboard**: `/dashboard` or `/user/dashboard` - Main user dashboard
- **Profile**: `/user/profile` - User profile management
- **Courses**: `/user/courses` - Course enrollment and management
- **Progress**: `/user/progress` - Learning progress tracking
- **Certificates**: `/user/certificates` - Certificate collection
- **Settings**: `/user/settings` - User preferences

#### Tutor Dashboard (Tutors, Admins, Super Admins only)
- **Tutor Dashboard**: `/tutor/dashboard` - Main tutor dashboard
- **My Courses**: `/tutor/courses` - Course management
- **Create Course**: `/tutor/create-course` - Course creation
- **Students**: `/tutor/students` - Student management
- **Analytics**: `/tutor/analytics` - Course analytics
- **Earnings**: `/tutor/earnings` - Revenue tracking
- **Tutor Profile**: `/tutor/profile` - Tutor profile
- **Tutor Settings**: `/tutor/settings` - Tutor preferences
- **Forgot Password**: `/password/request` - Password reset request
- **Logout**: `/logout` - Logout (POST request)

### 🎨 **Features Implemented**

✅ **UUID Primary Keys** - All tables use UUID instead of auto-incrementing IDs
✅ **Laravel Fortify** - Complete authentication system
✅ **Consistent Design** - Same color palette as homepage (#FF6B35)
✅ **Responsive Layout** - Works on desktop and mobile
✅ **Form Validation** - Real-time validation with error messages
✅ **Password Security** - Hashed passwords with strong validation rules
✅ **Remember Me** - Optional persistent login
✅ **CSRF Protection** - All forms protected against CSRF attacks

### 🔧 **Database Structure**

All tables now use UUID primary keys for consistency:

- **users** - UUID primary key
- **failed_jobs** - UUID primary key
- **personal_access_tokens** - UUID primary key with UUID morphs
- **password_reset_tokens** - Email as primary key (no change needed)

### 🎯 **Quick Test Steps**

1. Open browser to http://127.0.0.1:8000
2. Click "Masuk" button
3. Enter: `<EMAIL>` / `password123`
4. Click "Masuk" to login
5. You should see the dashboard with user info and UUID displayed

### 🔄 **Reset Database (if needed)**

If you need to reset the database and recreate test users:

```bash
php artisan migrate:fresh --seed
```

This will drop all tables, recreate them with UUID structure, and seed the test users.

---

**Note**: All users are pre-verified (email_verified_at is set), so no email verification is required for testing.
