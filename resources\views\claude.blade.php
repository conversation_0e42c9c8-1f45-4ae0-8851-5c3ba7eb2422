<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ngambiskuy - Learn Tech Skills</title>
    
    <!-- Include Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- AOS CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <style>
        /* Custom styles */
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-float {
            animation: float 6s ease-in-out infinite;
        }

        .hero-gradient {
            background: linear-gradient(-45deg, #667eea, #764ba2, #6B8DD6, #8E37D7);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 30px rgba(0,0,0,0.1);
        }

        .text-gradient {
            background: linear-gradient(to right, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .btn-hover {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-hover:after {
            content: '';
            position: absolute;
            width: 0%;
            height: 100%;
            top: 0;
            left: -40px;
            transform: skewX(-45deg);
            background: rgba(255,255,255,0.2);
            transition: all 0.5s ease;
        }

        .btn-hover:hover:after {
            width: 140%;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 600px;
        }
        
        .course-card {
            transition: transform 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }
        
        .stats-section {
            background: #f7fafc;
        }
        
        .testimonial-card {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }
    </style>
</head>

<body class="font-sans antialiased">
    <!-- Navigation -->
    <nav class="bg-white shadow">
        <div class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <div class="text-2xl font-bold text-gray-800">
                    Ngambiskuy
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#" class="text-gray-600 hover:text-gray-900">Courses</a>
                    <a href="#" class="text-gray-600 hover:text-gray-900">About</a>
                    <a href="#" class="text-gray-600 hover:text-gray-900">Contact</a>
                    <a href="#" class="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700">
                        Get Started
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-gradient text-white overflow-hidden">
        <div class="container mx-auto px-6 py-20">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2" data-aos="fade-right">
                    <h1 class="text-5xl font-bold mb-6 leading-tight">
                        Master <span class="relative">
                            Tech Skills
                            <svg class="absolute -bottom-2 w-full" viewBox="0 0 200 10" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0,5 Q50,2 100,5 T200,5" stroke="white" stroke-width="2" fill="none"/>
                            </svg>
                        </span> with Expert Guidance
                    </h1>
                <p class="text-xl mb-8">
                    Join our community of learners and gain hands-on experience in Web Development, 
                    Data Science, Game Development, and more.
                </p>
                <div class="flex space-x-4">
                    <a href="#" class="bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100">
                        Explore Free Courses
                    </a>
                    <a href="#" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-indigo-600">
                        View Premium Courses
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Courses -->
    <section class="py-20">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-center mb-12">Popular Courses</h2>
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Course Card 1 -->
                <div class="course-card bg-white shadow card-hover" data-aos="fade-up" data-aos-delay="100">
                    <img src="https://placehold.co/400x300" alt="Web Development" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm text-indigo-600 font-semibold">Web Development</span>
                            <span class="text-sm text-gray-500">12 weeks</span>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">Full Stack Web Development</h3>
                        <p class="text-gray-600 mb-4">Master modern web development with HTML, CSS, JavaScript, and popular frameworks.</p>
                        <div class="flex justify-between items-center">
                            <span class="font-bold text-lg">$299</span>
                            <a href="#" class="text-indigo-600 font-semibold hover:text-indigo-800">
                                Learn More →
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Course Card 2 -->
                <div class="course-card bg-white shadow" data-aos="fade-up" data-aos-delay="100">
                    <img src="https://placehold.co/400x300" alt="Data Science" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm text-indigo-600 font-semibold">Data Science</span>
                            <span class="text-sm text-gray-500">8 weeks</span>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">Data Science Fundamentals</h3>
                        <p class="text-gray-600 mb-4">Learn Python, statistics, and machine learning basics for data analysis.</p>
                        <div class="flex justify-between items-center">
                            <span class="font-bold text-lg">Free</span>
                            <a href="#" class="text-indigo-600 font-semibold hover:text-indigo-800">
                                Learn More →
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Course Card 3 -->
                <div class="course-card bg-white shadow" data-aos="fade-up" data-aos-delay="100">
                    <img src="https://placehold.co/400x300" alt="Game Development" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm text-indigo-600 font-semibold">Game Development</span>
                            <span class="text-sm text-gray-500">10 weeks</span>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">Unity Game Development</h3>
                        <p class="text-gray-600 mb-4">Create your own games using Unity engine and C# programming.</p>
                        <div class="flex justify-between items-center">
                            <span class="font-bold text-lg">$199</span>
                            <a href="#" class="text-indigo-600 font-semibold hover:text-indigo-800">
                                Learn More →
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section py-20 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10"></div>
        <div class="container mx-auto px-6 relative">
            <div class="grid md:grid-cols-4 gap-8 text-center">
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        const animateValue = (obj, start, end, duration) => {
                            let startTimestamp = null;
                            const step = (timestamp) => {
                                if (!startTimestamp) startTimestamp = timestamp;
                                const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                                obj.innerHTML = Math.floor(progress * (end - start) + start);
                                if (progress < 1) {
                                    window.requestAnimationFrame(step);
                                }
                            };
                            window.requestAnimationFrame(step);
                        };

                        const observer = new IntersectionObserver((entries) => {
                            entries.forEach(entry => {
                                if (entry.isIntersecting) {
                                    const el = entry.target;
                                    const finalValue = parseInt(el.getAttribute('data-value'));
                                    animateValue(el, 0, finalValue, 2000);
                                    observer.unobserve(el);
                                }
                            });
                        }, { threshold: 0.5 });

                        document.querySelectorAll('.stat-number').forEach(el => observer.observe(el));
                    });
                </script>
                <div>
                    <div class="stat-number text-4xl font-bold text-indigo-600 mb-2" data-value="5000">0</div>
                    <div class="text-gray-600">Active Students</div>
                </div>
                <div>
                    <div class="text-4xl font-bold text-indigo-600 mb-2 ">50+</div>
                    <div class="text-gray-600">Expert Instructors</div>
                </div>
                <div>
                    <div class="text-4xl font-bold text-indigo-600 mb-2">100+</div>
                    <div class="text-gray-600">Courses Available</div>
                </div>
                <div>
                    <div class="text-4xl font-bold text-indigo-600 mb-2">95%</div>
                    <div class="text-gray-600">Success Rate</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="py-20">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-center mb-12">What Our Students Say</h2>
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Testimonial 1 -->
                <div class="testimonial-card">
                    <p class="text-gray-600 mb-6">"The web development course was amazing! I learned so much and now I have a great job as a frontend developer."</p>
                    <div class="flex items-center">
                        <img src="https://placehold.co/100x100" alt="Student" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <div class="font-semibold">Sarah Johnson</div>
                            <div class="text-gray-500 text-sm">Web Development</div>
                        </div>
                    </div>
                </div>

                <!-- Testimonial 2 -->
                <div class="testimonial-card">
                    <p class="text-gray-600 mb-6">"The data science course gave me the foundation I needed to switch careers. The instructors were very helpful."</p>
                    <div class="flex items-center">
                        <img src="https://placehold.co/100x100" alt="Student" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <div class="font-semibold">Michael Chen</div>
                            <div class="text-gray-500 text-sm">Data Science</div>
                        </div>
                    </div>
                </div>

                <!-- Testimonial 3 -->
                <div class="testimonial-card">
                    <p class="text-gray-600 mb-6">"I published my first game after taking the Unity course. The practical projects really helped me understand game development."</p>
                    <div class="flex items-center">
                        <img src="https://placehold.co/100x100" alt="Student" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <div class="font-semibold">Emily Rodriguez</div>
                            <div class="text-gray-500 text-sm">Game Development</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="bg-indigo-600 text-white py-20">
        <div class="container mx-auto px-6 text-center">
            <h2 class="text-3xl font-bold mb-8">Ready to Start Your Tech Journey?</h2>
            <p class="text-xl mb-8">Join thousands of students already learning with Ngambiskuy</p>
            <a href="#" class="bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 inline-block">
                Get Started Now
            </a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="container mx-auto px-6 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-2xl font-bold mb-4">Ngambiskuy</h3>
                    <p class="text-gray-400">
                        Empowering the next generation of tech professionals through quality education.
                    </p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Courses</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">Web Development</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Data Science</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Game Development</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Mobile Development</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">About Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Careers</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Blog</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Connect With Us</h4>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
                <p>&copy; 2024 Ngambiskuy. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- AOS JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // Smooth scroll for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add parallax effect to hero section
        window.addEventListener('scroll', function() {
            const parallax = document.querySelector('.hero-gradient');
            let scrollPosition = window.pageYOffset;
            parallax.style.backgroundPositionY = scrollPosition * 0.5 + 'px';
        });
    </script>
</body>
</html>