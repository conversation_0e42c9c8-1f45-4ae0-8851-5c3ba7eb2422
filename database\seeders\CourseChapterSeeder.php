<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\CourseChapter;
use Illuminate\Database\Seeder;

class CourseChapterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get courses to add chapters to
        $courses = Course::all();

        foreach ($courses as $course) {
            $this->createChaptersForCourse($course);
        }
    }

    private function createChaptersForCourse(Course $course)
    {
        $chapters = [];

        if ($course->title === 'Introduction to Programming') {
            $chapters = [
                [
                    'title' => 'Bab 1: Pengenalan Programming',
                    'description' => 'Memahami dasar-dasar pemrograman dan konsep fundamental',
                    'is_published' => true,
                    'is_free' => true,
                ],
                [
                    'title' => 'Bab 2: Variabel dan Tipe Data',
                    'description' => 'Mempelajari cara menyimpan dan mengelola data dalam program',
                    'is_published' => true,
                    'is_free' => true,
                ],
                [
                    'title' => 'Bab 3: Struktur Kontrol',
                    'description' => 'Menguasai penggunaan if-else, loop, dan struktur kontrol lainnya',
                    'is_published' => true,
                    'is_free' => true,
                ],
                [
                    'title' => 'Bab 4: Fungsi dan Prosedur',
                    'description' => 'Membuat kode yang dapat digunakan kembali dengan fungsi',
                    'is_published' => false,
                    'is_free' => true,
                ],
            ];
        } elseif ($course->title === 'HTML & CSS Fundamentals') {
            $chapters = [
                [
                    'title' => 'Bab 1: Pengenalan HTML',
                    'description' => 'Memahami struktur dasar HTML dan elemen-elemennya',
                    'is_published' => true,
                    'is_free' => true,
                ],
                [
                    'title' => 'Bab 2: CSS Styling',
                    'description' => 'Mempelajari cara mendesain tampilan website dengan CSS',
                    'is_published' => true,
                    'is_free' => true,
                ],
                [
                    'title' => 'Bab 3: Responsive Design',
                    'description' => 'Membuat website yang responsif di berbagai perangkat',
                    'is_published' => true,
                    'is_free' => true,
                ],
            ];
        } elseif ($course->title === 'Frontend Development dengan React') {
            $chapters = [
                [
                    'title' => 'Bab 1: React Fundamentals',
                    'description' => 'Memahami konsep dasar React dan JSX',
                    'is_published' => true,
                    'is_free' => true,
                ],
                [
                    'title' => 'Bab 2: Components dan Props',
                    'description' => 'Membuat dan menggunakan komponen React',
                    'is_published' => true,
                    'is_free' => false,
                ],
                [
                    'title' => 'Bab 3: State Management',
                    'description' => 'Mengelola state dalam aplikasi React',
                    'is_published' => true,
                    'is_free' => false,
                ],
                [
                    'title' => 'Bab 4: React Hooks',
                    'description' => 'Menggunakan hooks untuk functional components',
                    'is_published' => true,
                    'is_free' => false,
                ],
                [
                    'title' => 'Bab 5: Routing dan Navigation',
                    'description' => 'Implementasi routing dalam aplikasi React',
                    'is_published' => false,
                    'is_free' => false,
                ],
            ];
        } elseif ($course->title === 'Backend Development dengan Laravel') {
            $chapters = [
                [
                    'title' => 'Bab 1: Laravel Setup',
                    'description' => 'Instalasi dan konfigurasi Laravel framework',
                    'is_published' => true,
                    'is_free' => true,
                ],
                [
                    'title' => 'Bab 2: MVC Architecture',
                    'description' => 'Memahami pola Model-View-Controller dalam Laravel',
                    'is_published' => true,
                    'is_free' => false,
                ],
                [
                    'title' => 'Bab 3: Database dan Eloquent',
                    'description' => 'Bekerja dengan database menggunakan Eloquent ORM',
                    'is_published' => true,
                    'is_free' => false,
                ],
                [
                    'title' => 'Bab 4: API Development',
                    'description' => 'Membuat REST API dengan Laravel',
                    'is_published' => false,
                    'is_free' => false,
                ],
            ];
        } elseif ($course->title === 'Python for Data Science') {
            $chapters = [
                [
                    'title' => 'Bab 1: Python Basics',
                    'description' => 'Dasar-dasar bahasa pemrograman Python',
                    'is_published' => true,
                    'is_free' => true,
                ],
                [
                    'title' => 'Bab 2: NumPy dan Pandas',
                    'description' => 'Library fundamental untuk data science',
                    'is_published' => true,
                    'is_free' => false,
                ],
                [
                    'title' => 'Bab 3: Data Visualization',
                    'description' => 'Membuat visualisasi data dengan Matplotlib dan Seaborn',
                    'is_published' => false,
                    'is_free' => false,
                ],
            ];
        } elseif ($course->title === 'Introduction to Data Science') {
            $chapters = [
                [
                    'title' => 'Bab 1: Apa itu Data Science?',
                    'description' => 'Pengenalan konsep dan aplikasi data science',
                    'is_published' => true,
                    'is_free' => true,
                ],
                [
                    'title' => 'Bab 2: Data Collection',
                    'description' => 'Metode pengumpulan dan sumber data',
                    'is_published' => true,
                    'is_free' => true,
                ],
                [
                    'title' => 'Bab 3: Data Analysis',
                    'description' => 'Teknik analisis data dasar',
                    'is_published' => false,
                    'is_free' => true,
                ],
            ];
        } else {
            // Default chapters for other courses
            $chapters = [
                [
                    'title' => 'Bab 1: Pengenalan',
                    'description' => 'Pengenalan dasar materi kursus',
                    'is_published' => true,
                    'is_free' => $course->is_free,
                ],
                [
                    'title' => 'Bab 2: Praktik',
                    'description' => 'Latihan dan praktik langsung',
                    'is_published' => false,
                    'is_free' => false,
                ],
            ];
        }

        foreach ($chapters as $index => $chapterData) {
            CourseChapter::create([
                'course_id' => $course->id,
                'title' => $chapterData['title'],
                'description' => $chapterData['description'],
                'sort_order' => $index + 1,
                'is_published' => $chapterData['is_published'],
                'is_free' => $chapterData['is_free'],
            ]);
        }
    }
}
