<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lesson_assignments', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('lesson_id'); // Foreign key to course_lessons table
            
            // Assignment Information
            $table->string('title'); // Assignment title
            $table->longText('description'); // Assignment description/instructions (supports rich text)
            $table->longText('requirements')->nullable(); // Assignment requirements
            $table->json('allowed_file_types')->nullable(); // Allowed file types for submission
            $table->integer('max_file_size')->default(10); // Max file size in MB
            $table->integer('max_files')->default(5); // Maximum number of files
            $table->datetime('due_date')->nullable(); // Assignment due date
            $table->integer('max_points')->default(100); // Maximum points for this assignment
            $table->boolean('allow_late_submission')->default(false);
            $table->integer('late_penalty_percent')->default(0); // Penalty percentage for late submission
            $table->boolean('is_published')->default(false);
            
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('lesson_id')->references('id')->on('course_lessons')->onDelete('cascade');
            
            // Indexes
            $table->index('lesson_id');
            $table->index('due_date');
            $table->index('is_published');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lesson_assignments');
    }
};
