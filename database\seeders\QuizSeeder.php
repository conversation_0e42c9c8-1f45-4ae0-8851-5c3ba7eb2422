<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\CourseLesson;
use App\Models\LessonQuiz;
use App\Models\QuizQuestion;
use App\Models\QuizQuestionOption;

class QuizSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some quiz-type lessons
        $quizLessons = CourseLesson::where('type', 'quiz')->take(3)->get();

        foreach ($quizLessons as $lesson) {
            // Create a quiz for this lesson
            $quiz = LessonQuiz::create([
                'lesson_id' => $lesson->id,
                'title' => 'Kuis: ' . $lesson->title,
                'description' => 'Kuis untuk menguji pemahaman Anda tentang materi ' . $lesson->title,
                'time_limit' => 30, // 30 minutes
                'max_attempts' => 3,
                'passing_score' => 70,
                'shuffle_questions' => true,
                'show_results_immediately' => true,
                'is_published' => true,
            ]);

            // Create questions for this quiz
            $this->createQuestionsForQuiz($quiz);
        }
    }

    private function createQuestionsForQuiz(LessonQuiz $quiz): void
    {
        // Question 1: Multiple Choice
        $question1 = QuizQuestion::create([
            'quiz_id' => $quiz->id,
            'question' => 'Apa yang dimaksud dengan variabel dalam programming?',
            'type' => 'multiple_choice',
            'points' => 10,
            'sort_order' => 1,
            'explanation' => 'Variabel adalah tempat penyimpanan data yang nilainya dapat berubah selama program berjalan.',
            'is_required' => true,
        ]);

        // Options for question 1
        QuizQuestionOption::create([
            'question_id' => $question1->id,
            'option_text' => 'Tempat penyimpanan data yang nilainya dapat berubah',
            'is_correct' => true,
            'sort_order' => 1,
        ]);

        QuizQuestionOption::create([
            'question_id' => $question1->id,
            'option_text' => 'Fungsi untuk menampilkan output',
            'is_correct' => false,
            'sort_order' => 2,
        ]);

        QuizQuestionOption::create([
            'question_id' => $question1->id,
            'option_text' => 'Struktur kontrol untuk perulangan',
            'is_correct' => false,
            'sort_order' => 3,
        ]);

        QuizQuestionOption::create([
            'question_id' => $question1->id,
            'option_text' => 'Operator untuk operasi matematika',
            'is_correct' => false,
            'sort_order' => 4,
        ]);

        // Question 2: True/False
        $question2 = QuizQuestion::create([
            'quiz_id' => $quiz->id,
            'question' => 'JavaScript adalah bahasa pemrograman yang hanya bisa dijalankan di browser.',
            'type' => 'true_false',
            'points' => 5,
            'sort_order' => 2,
            'explanation' => 'JavaScript sekarang dapat dijalankan di server menggunakan Node.js, tidak hanya di browser.',
            'is_required' => true,
        ]);

        // Options for question 2
        QuizQuestionOption::create([
            'question_id' => $question2->id,
            'option_text' => 'Benar',
            'is_correct' => false,
            'sort_order' => 1,
        ]);

        QuizQuestionOption::create([
            'question_id' => $question2->id,
            'option_text' => 'Salah',
            'is_correct' => true,
            'sort_order' => 2,
        ]);

        // Question 3: Multiple Choice
        $question3 = QuizQuestion::create([
            'quiz_id' => $quiz->id,
            'question' => 'Manakah dari berikut ini yang merupakan tipe data primitif dalam JavaScript?',
            'type' => 'multiple_choice',
            'points' => 10,
            'sort_order' => 3,
            'explanation' => 'String, Number, Boolean, Undefined, Null, Symbol, dan BigInt adalah tipe data primitif dalam JavaScript.',
            'is_required' => true,
        ]);

        // Options for question 3
        QuizQuestionOption::create([
            'question_id' => $question3->id,
            'option_text' => 'String',
            'is_correct' => true,
            'sort_order' => 1,
        ]);

        QuizQuestionOption::create([
            'question_id' => $question3->id,
            'option_text' => 'Array',
            'is_correct' => false,
            'sort_order' => 2,
        ]);

        QuizQuestionOption::create([
            'question_id' => $question3->id,
            'option_text' => 'Object',
            'is_correct' => false,
            'sort_order' => 3,
        ]);

        QuizQuestionOption::create([
            'question_id' => $question3->id,
            'option_text' => 'Function',
            'is_correct' => false,
            'sort_order' => 4,
        ]);

        // Question 4: Short Answer
        $question4 = QuizQuestion::create([
            'quiz_id' => $quiz->id,
            'question' => 'Sebutkan 3 cara untuk mendeklarasikan variabel dalam JavaScript modern.',
            'type' => 'short_answer',
            'points' => 15,
            'sort_order' => 4,
            'explanation' => 'Tiga cara mendeklarasikan variabel dalam JavaScript modern adalah: var, let, dan const.',
            'is_required' => true,
        ]);

        // Question 5: Essay
        $question5 = QuizQuestion::create([
            'quiz_id' => $quiz->id,
            'question' => 'Jelaskan perbedaan antara let, const, dan var dalam JavaScript. Berikan contoh penggunaan masing-masing.',
            'type' => 'essay',
            'points' => 20,
            'sort_order' => 5,
            'explanation' => 'var memiliki function scope, let dan const memiliki block scope. const tidak dapat diubah setelah deklarasi, sedangkan let dapat diubah.',
            'is_required' => true,
        ]);
    }
}
