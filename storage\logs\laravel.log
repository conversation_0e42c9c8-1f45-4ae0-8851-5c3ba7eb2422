
[2025-05-27 22:26:29] local.INFO: Course creation attempt {"user_id":"9f02d41a-b922-4498-89e4-6a37894255b7","request_data":{"_token":"RzLTyn3tqElqL7PLLabVYQ7gbCQ89BeEQI93filQ","course_title":"fesfsef","course_category":"9f02d419-d021-4894-bc5d-8894fe35eee1","course_description":"fesfseffesfseffesfseffesfseffesfseffesfseffesfsef","course_level":"beginner","course_duration":null,"course_type":"paid","course_price":"12333","learning_outcomes":[null,null,null],"requirements":[null,null],"target_audience":[null,null]},"has_file":false} 
[2025-05-27 22:26:34] local.INFO: Course creation attempt {"user_id":"9f02d41a-b922-4498-89e4-6a37894255b7","request_data":{"_token":"RzLTyn3tqElqL7PLLabVYQ7gbCQ89BeEQI93filQ","course_title":"fesfsef","course_category":"9f02d419-d021-4894-bc5d-8894fe35eee1","course_description":"fesfseffesfseffesfseffesfseffesfseffesfseffesfseffesfseffesfseffesfseffesfseffesfseffesfseffesfsef","course_level":"beginner","course_duration":null,"course_type":"paid","course_price":"12333","learning_outcomes":[null,null,null],"requirements":[null,null],"target_audience":[null,null]},"has_file":false} 
[2025-05-27 22:26:34] local.INFO: Creating course with data {"learning_outcomes":[],"requirements":[],"target_audience":[]} 
[2025-05-27 22:26:35] local.INFO: Course created successfully {"course_id":"9f03c177-75a9-439a-aac3-bc4278402cc2"} 
[2025-05-27 22:26:35] local.INFO: Redirecting to courses page  
[2025-05-27 22:52:31] local.ERROR: View [tutor.curriculum.create-material] not found. {"userId":"9f02d41a-b922-4498-89e4-6a37894255b7","exception":"[object] (InvalidArgumentException(code: 0): View [tutor.curriculum.create-material] not found. at D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:137)
[stacktrace]
#0 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(79): Illuminate\\View\\FileViewFinder->findInPaths('tutor.curriculu...', Array)
#1 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(138): Illuminate\\View\\FileViewFinder->find('tutor.curriculu...')
#2 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(1020): Illuminate\\View\\Factory->make('tutor.curriculu...', Array, Array)
#3 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\app\\Http\\Controllers\\Tutor\\CurriculumController.php(75): view('tutor.curriculu...', Array)
#4 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Tutor\\CurriculumController->createMaterial(Object(App\\Models\\Course), Object(App\\Models\\CourseChapter))
#5 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('createMaterial', Array)
#6 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Tutor\\CurriculumController), 'createMaterial')
#7 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#8 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#9 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\app\\Http\\Middleware\\IsTutor.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\IsTutor->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#51 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\public\\index.php(71): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#52 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\bps renata k...')
#53 {main}
"} 
[2025-05-27 22:52:47] local.ERROR: View [tutor.curriculum.create-material] not found. {"userId":"9f02d41a-b922-4498-89e4-6a37894255b7","exception":"[object] (InvalidArgumentException(code: 0): View [tutor.curriculum.create-material] not found. at D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:137)
[stacktrace]
#0 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(79): Illuminate\\View\\FileViewFinder->findInPaths('tutor.curriculu...', Array)
#1 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(138): Illuminate\\View\\FileViewFinder->find('tutor.curriculu...')
#2 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(1020): Illuminate\\View\\Factory->make('tutor.curriculu...', Array, Array)
#3 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\app\\Http\\Controllers\\Tutor\\CurriculumController.php(75): view('tutor.curriculu...', Array)
#4 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Tutor\\CurriculumController->createMaterial(Object(App\\Models\\Course), Object(App\\Models\\CourseChapter))
#5 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('createMaterial', Array)
#6 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Tutor\\CurriculumController), 'createMaterial')
#7 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#8 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#9 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\app\\Http\\Middleware\\IsTutor.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\IsTutor->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#51 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\public\\index.php(71): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#52 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\bps renata k...')
#53 {main}
"} 
[2025-05-27 22:53:41] local.ERROR: View [tutor.curriculum.create-material] not found. {"userId":"9f02d41a-b922-4498-89e4-6a37894255b7","exception":"[object] (InvalidArgumentException(code: 0): View [tutor.curriculum.create-material] not found. at D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:137)
[stacktrace]
#0 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(79): Illuminate\\View\\FileViewFinder->findInPaths('tutor.curriculu...', Array)
#1 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(138): Illuminate\\View\\FileViewFinder->find('tutor.curriculu...')
#2 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(1020): Illuminate\\View\\Factory->make('tutor.curriculu...', Array, Array)
#3 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\app\\Http\\Controllers\\Tutor\\CurriculumController.php(75): view('tutor.curriculu...', Array)
#4 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Tutor\\CurriculumController->createMaterial(Object(App\\Models\\Course), Object(App\\Models\\CourseChapter))
#5 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('createMaterial', Array)
#6 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Tutor\\CurriculumController), 'createMaterial')
#7 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#8 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#9 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\app\\Http\\Middleware\\IsTutor.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\IsTutor->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#51 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\public\\index.php(71): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#52 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\bps renata k...')
#53 {main}
"} 
[2025-05-27 22:58:31] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `ngambiskuy`.`lesson_quizzes` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `lesson_quizzes` add constraint `lesson_quizzes_lesson_id_foreign` foreign key (`lesson_id`) references `course_lessons` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `ngambiskuy`.`lesson_quizzes` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `lesson_quizzes` add constraint `lesson_quizzes_lesson_id_foreign` foreign key (`lesson_id`) references `course_lessons` (`id`) on delete cascade) at D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `le...', Array, Object(Closure))
#1 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('alter table `le...', Array, Object(Closure))
#2 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `le...')
#3 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('lesson_quizzes', Object(Closure))
#6 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\database\\migrations\\2025_01_27_120000_create_lesson_quizzes_table.php(36): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2025_01_27_1200...', Object(Closure))
#13 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_01_27_1200...', Object(Closure))
#14 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\bps renata k...', 1, false)
#15 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(92): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#29 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(62): Illuminate\\Console\\Command->call('migrate', Array)
#30 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#31 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#32 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#36 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#37 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `ngambiskuy`.`lesson_quizzes` (errno: 150 \"Foreign key constraint is incorrectly formed\") at D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `le...', Array)
#2 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `le...', Array, Object(Closure))
#3 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('alter table `le...', Array, Object(Closure))
#4 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `le...')
#5 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('lesson_quizzes', Object(Closure))
#8 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\database\\migrations\\2025_01_27_120000_create_lesson_quizzes_table.php(36): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2025_01_27_1200...', Object(Closure))
#15 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_01_27_1200...', Object(Closure))
#16 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\bps renata k...', 1, false)
#17 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(92): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#31 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(62): Illuminate\\Console\\Command->call('migrate', Array)
#32 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#33 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#36 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#37 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#38 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 {main}
"} 
[2025-05-27 23:01:05] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `ngambiskuy`.`lesson_assignments` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `lesson_assignments` add constraint `lesson_assignments_lesson_id_foreign` foreign key (`lesson_id`) references `course_lessons` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `ngambiskuy`.`lesson_assignments` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `lesson_assignments` add constraint `lesson_assignments_lesson_id_foreign` foreign key (`lesson_id`) references `course_lessons` (`id`) on delete cascade) at D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `le...', Array, Object(Closure))
#1 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('alter table `le...', Array, Object(Closure))
#2 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `le...')
#3 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('lesson_assignme...', Object(Closure))
#6 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\database\\migrations\\2025_01_27_120003_create_lesson_assignments_table.php(40): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2025_01_27_1200...', Object(Closure))
#13 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_01_27_1200...', Object(Closure))
#14 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\bps renata k...', 1, false)
#15 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(92): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#29 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(62): Illuminate\\Console\\Command->call('migrate', Array)
#30 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#31 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#32 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#36 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#37 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `ngambiskuy`.`lesson_assignments` (errno: 150 \"Foreign key constraint is incorrectly formed\") at D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `le...', Array)
#2 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('alter table `le...', Array, Object(Closure))
#3 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('alter table `le...', Array, Object(Closure))
#4 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('alter table `le...')
#5 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('lesson_assignme...', Object(Closure))
#8 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\database\\migrations\\2025_01_27_120003_create_lesson_assignments_table.php(40): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2025_01_27_1200...', Object(Closure))
#15 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_01_27_1200...', Object(Closure))
#16 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\bps renata k...', 1, false)
#17 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(92): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#31 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(62): Illuminate\\Console\\Command->call('migrate', Array)
#32 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#33 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#36 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#37 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#38 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 {main}
"} 
[2025-05-27 23:01:49] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'lesson_quizzes' already exists (Connection: mysql, SQL: create table `lesson_quizzes` (`id` char(36) not null, `lesson_id` char(36) not null, `title` varchar(255) not null, `description` text null, `time_limit` int null, `max_attempts` int not null default '1', `passing_score` int not null default '70', `shuffle_questions` tinyint(1) not null default '0', `show_results_immediately` tinyint(1) not null default '1', `is_published` tinyint(1) not null default '0', `created_at` timestamp null, `updated_at` timestamp null, primary key (`id`)) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'lesson_quizzes' already exists (Connection: mysql, SQL: create table `lesson_quizzes` (`id` char(36) not null, `lesson_id` char(36) not null, `title` varchar(255) not null, `description` text null, `time_limit` int null, `max_attempts` int not null default '1', `passing_score` int not null default '70', `shuffle_questions` tinyint(1) not null default '0', `show_results_immediately` tinyint(1) not null default '1', `is_published` tinyint(1) not null default '0', `created_at` timestamp null, `updated_at` timestamp null, primary key (`id`)) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table `l...', Array, Object(Closure))
#1 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('create table `l...', Array, Object(Closure))
#2 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `l...')
#3 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('lesson_quizzes', Object(Closure))
#6 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\database\\migrations\\2025_05_27_130000_create_lesson_quizzes_table.php(36): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2025_05_27_1300...', Object(Closure))
#13 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_27_1300...', Object(Closure))
#14 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\bps renata k...', 1, false)
#15 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(92): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#29 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(62): Illuminate\\Console\\Command->call('migrate', Array)
#30 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#31 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#32 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#36 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#37 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'lesson_quizzes' already exists at D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `l...', Array)
#2 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table `l...', Array, Object(Closure))
#3 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('create table `l...', Array, Object(Closure))
#4 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `l...')
#5 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('lesson_quizzes', Object(Closure))
#8 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\database\\migrations\\2025_05_27_130000_create_lesson_quizzes_table.php(36): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2025_05_27_1300...', Object(Closure))
#15 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_27_1300...', Object(Closure))
#16 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\bps renata k...', 1, false)
#17 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(92): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#31 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(62): Illuminate\\Console\\Command->call('migrate', Array)
#32 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#33 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#36 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#37 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#38 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 D:\\bps renata kerja\\2024\\project stat sektoral website\\Taylor-Swift-Web-Project-main\\ngambiskuy\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 {main}
"} 
