<!-- AI Chat Component for Ngambiskuy -->
<div id="ai-chat-container" class="fixed bottom-6 right-6 z-50">
    <!-- Chat Toggle Button -->
    <button id="ai-chat-toggle" class="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center justify-center group">
        <svg id="chat-icon" class="w-7 h-7 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
        <svg id="close-icon" class="w-7 h-7 transition-transform duration-300 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>

        <!-- Notification Badge -->
        <div id="chat-notification" class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center hidden">
            <span id="notification-count">1</span>
        </div>
    </button>

    <!-- Chat Window -->
    <div id="ai-chat-window" class="absolute bottom-20 right-0 w-96 h-[500px] bg-white rounded-2xl shadow-2xl border border-gray-200 transform scale-0 origin-bottom-right transition-all duration-300 opacity-0 hidden">
        <!-- Chat Header -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-t-2xl">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-semibold text-lg">AI Assistant</h3>
                        <p class="text-sm text-white/80">Intelligent Course Engine</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <span class="text-sm">Online</span>
                </div>
            </div>
        </div>

        <!-- Chat Messages -->
        <div id="chat-messages" class="flex-1 p-4 h-80 overflow-y-auto bg-gray-50">
            <!-- Welcome Message -->
            <div class="mb-4">
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm max-w-xs">
                        <p class="text-gray-800 text-sm">Halo! Saya AI Assistant Ngambiskuy. Saya dapat membantu Anda dengan:</p>
                        <ul class="mt-2 text-xs text-gray-600 space-y-1">
                            <li>• Rekomendasi kursus yang tepat</li>
                            <li>• Membuat jalur pembelajaran personal</li>
                            <li>• Analisis karir dan skill gap</li>
                            <li>• Pertanyaan tentang platform</li>
                        </ul>
                    </div>
                </div>
                <span class="text-xs text-gray-500 ml-11">Baru saja</span>
            </div>
        </div>

        <!-- Chat Input -->
        <div class="p-4 border-t border-gray-200 bg-white rounded-b-2xl">
            <div class="flex items-center space-x-3">
                <div class="flex-1 relative">
                    <input
                        type="text"
                        id="chat-input"
                        placeholder="Ketik pesan Anda..."
                        class="w-full px-4 py-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        maxlength="500"
                    >
                    <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <span id="char-count" class="text-xs text-gray-400">0/500</span>
                    </div>
                </div>
                <button
                    id="send-message"
                    class="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full hover:shadow-lg transform hover:scale-105 transition-all duration-200 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>

            <!-- Quick Actions -->
            <div class="mt-3 flex flex-wrap gap-2">
                <button class="quick-action-btn px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs hover:bg-gray-200 transition-colors">
                    Rekomendasi Kursus
                </button>
                <button class="quick-action-btn px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs hover:bg-gray-200 transition-colors">
                    Jalur Karir
                </button>
                <button class="quick-action-btn px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs hover:bg-gray-200 transition-colors">
                    Bantuan
                </button>
            </div>
        </div>
    </div>
</div>

<!-- AI Chat Styles -->
<style>
    #ai-chat-window {
        max-height: 90vh;
        min-height: 400px;
    }

    @media (max-width: 640px) {
        #ai-chat-window {
            width: calc(100vw - 2rem);
            right: 1rem;
            left: 1rem;
            bottom: 5rem;
        }
    }

    #chat-messages::-webkit-scrollbar {
        width: 4px;
    }

    #chat-messages::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    #chat-messages::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
    }

    #chat-messages::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    .message-animation {
        animation: slideInUp 0.3s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .typing-indicator {
        display: flex;
        align-items: center;
        space-x: 1;
    }

    .typing-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #9CA3AF;
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-dot:nth-child(1) { animation-delay: -0.32s; }
    .typing-dot:nth-child(2) { animation-delay: -0.16s; }

    @keyframes typing {
        0%, 80%, 100% {
            transform: scale(0);
            opacity: 0.5;
        }
        40% {
            transform: scale(1);
            opacity: 1;
        }
    }
</style>

<!-- AI Chat JavaScript is loaded via Vite -->
@push('scripts')
@vite(['resources/js/ai-chat.js'])
@endpush
