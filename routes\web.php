<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\HomepageController;
use App\Http\Controllers\CurriculumController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\UserProfileController;
use App\Http\Controllers\TutorController;
use App\Http\Controllers\TutorRegistrationController;
use App\Http\Controllers\Tutor\CurriculumController as TutorCurriculumController;
use App\Http\Controllers\Admin\TutorApplicationController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', [HomeController::class, 'index'])->name('home');

// Curriculum routes (public)
Route::get('/kurikulum', [CurriculumController::class, 'index'])->name('curriculum.index');
Route::get('/kursus/{course}', [CurriculumController::class, 'show'])->name('course.show');

Route::get('/deepseek',[HomepageController::class, 'deepseek']);
Route::get('/claude',[HomepageController::class, 'claude']);
Route::get('/chatgpt',[HomepageController::class, 'chatgpt']);



// User Dashboard routes (protected by auth middleware)
Route::middleware('auth')->prefix('user')->name('user.')->group(function () {
    Route::get('/dashboard', [UserController::class, 'dashboard'])->name('dashboard');
    Route::get('/profile', [UserProfileController::class, 'show'])->name('profile');
    Route::put('/profile', [UserProfileController::class, 'update'])->name('profile.update');
    Route::post('/profile/picture', [UserProfileController::class, 'updateProfilePicture'])->name('profile.picture.update');
    Route::delete('/profile/picture', [UserProfileController::class, 'deleteProfilePicture'])->name('profile.delete-picture');
    Route::get('/courses', [UserController::class, 'courses'])->name('courses');
    Route::get('/progress', [UserController::class, 'progress'])->name('progress');
    Route::get('/certificates', [UserController::class, 'certificates'])->name('certificates');
    Route::get('/settings', [UserController::class, 'settings'])->name('settings');
});

// Tutor Registration routes (protected by auth middleware)
Route::middleware('auth')->prefix('tutor/register')->name('tutor.register.')->group(function () {
    Route::get('/terms', [TutorRegistrationController::class, 'showTerms'])->name('terms');
    Route::post('/terms', [TutorRegistrationController::class, 'processTerms'])->name('terms.process');
    Route::get('/profile', [TutorRegistrationController::class, 'showProfile'])->name('profile');
    Route::post('/profile', [TutorRegistrationController::class, 'processProfile'])->name('profile.process');
    Route::get('/review', [TutorRegistrationController::class, 'showReview'])->name('review');
    Route::post('/submit', [TutorRegistrationController::class, 'submitApplication'])->name('submit');
    Route::get('/status', [TutorRegistrationController::class, 'showStatus'])->name('status');
});

// Tutor Dashboard routes (protected by auth and is.tutor middleware)
Route::middleware(['auth', 'is.tutor'])->prefix('tutor')->name('tutor.')->group(function () {
    Route::get('/dashboard', [TutorController::class, 'dashboard'])->name('dashboard');
    Route::get('/courses', [TutorController::class, 'courses'])->name('courses');
    Route::get('/create-course', [TutorController::class, 'createCourse'])->name('create-course');
    Route::post('/create-course', [TutorController::class, 'storeCourse'])->name('store-course');

    // Curriculum management routes
    Route::get('/courses/{course}/curriculum', [TutorCurriculumController::class, 'index'])->name('curriculum.index');
    Route::post('/courses/{course}/chapters', [TutorCurriculumController::class, 'storeChapter'])->name('curriculum.store-chapter');
    Route::get('/courses/{course}/chapters/{chapter}/materials/create', [TutorCurriculumController::class, 'createMaterial'])->name('curriculum.create-material');
    Route::post('/courses/{course}/chapters/{chapter}/lessons', [TutorCurriculumController::class, 'storeLesson'])->name('curriculum.store-lesson');
    Route::get('/courses/{course}/chapters/{chapter}/materials/{lesson}/edit', [TutorCurriculumController::class, 'editMaterial'])->name('curriculum.edit-material');
    Route::post('/courses/{course}/upload-video', [TutorCurriculumController::class, 'uploadVideo'])->name('curriculum.upload-video');
    Route::put('/courses/{course}/chapters/{chapter}', [TutorCurriculumController::class, 'updateChapter'])->name('curriculum.update-chapter');
    Route::put('/courses/{course}/chapters/{chapter}/lessons/{lesson}', [TutorCurriculumController::class, 'updateLesson'])->name('curriculum.update-lesson');
    Route::delete('/courses/{course}/chapters/{chapter}', [TutorCurriculumController::class, 'deleteChapter'])->name('curriculum.delete-chapter');
    Route::delete('/courses/{course}/chapters/{chapter}/lessons/{lesson}', [TutorCurriculumController::class, 'deleteLesson'])->name('curriculum.delete-lesson');

    Route::get('/students', [TutorController::class, 'students'])->name('students');
    Route::get('/analytics', [TutorController::class, 'analytics'])->name('analytics');
    Route::get('/earnings', [TutorController::class, 'earnings'])->name('earnings');
    Route::get('/profile', [TutorController::class, 'profile'])->name('profile');
    Route::put('/profile', [TutorController::class, 'updateProfile'])->name('profile.update');
    Route::get('/settings', [TutorController::class, 'settings'])->name('settings');
});

// Secure File Access Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/secure/courses/{course}/lessons/{lesson}/video', [App\Http\Controllers\SecureFileController::class, 'serveVideo'])->name('secure.lesson.video');
    Route::get('/secure/courses/{course}/lessons/{lesson}/material/{filename}', [App\Http\Controllers\SecureFileController::class, 'serveMaterial'])->name('secure.lesson.material');
});

// Admin routes (protected by auth and is.admin middleware)
Route::middleware(['auth', 'is.admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/tutor-applications', [TutorApplicationController::class, 'index'])->name('tutor-applications.index');
    Route::get('/tutor-applications/{application}', [TutorApplicationController::class, 'show'])->name('tutor-applications.show');
    Route::post('/tutor-applications/{application}/review', [TutorApplicationController::class, 'review'])->name('tutor-applications.review');
    Route::post('/tutor-applications/{application}/approve', [TutorApplicationController::class, 'approve'])->name('tutor-applications.approve');
    Route::post('/tutor-applications/{application}/reject', [TutorApplicationController::class, 'reject'])->name('tutor-applications.reject');
    Route::get('/tutor-applications/{application}/download/{type}', [TutorApplicationController::class, 'downloadFile'])->name('tutor-applications.download');
});

// Redirect old dashboard route to new structure
Route::get('/dashboard', function () {
    return redirect()->route('user.dashboard');
})->middleware('auth')->name('dashboard');

// Public tutor profile route (must be last to avoid conflicts with specific routes)
Route::get('/tutor/{slug}', function ($slug) {
    $profile = \App\Models\TutorProfile::bySlug($slug)->with('user')->first();

    if (!$profile || $profile->status !== 'approved') {
        abort(404, 'Tutor tidak ditemukan atau belum disetujui.');
    }

    return view('tutor.public-profile', compact('profile'));
})->name('tutor.public-profile');

