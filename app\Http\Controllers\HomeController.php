<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        // Sample data for homepage
        $courses = [
            // Free Courses
            [
                'id' => 1,
                'title' => 'Introduction to Programming',
                'description' => 'Learn the basics of programming with hands-on exercises',
                'category' => 'Programming',
                'level' => 'Pemula',
                'price' => 'Free',
                'rating' => 4.6,
                'students' => 2150,
                'duration' => '4 hours',
                'image' => 'courses/placeholder.svg',
                'type' => 'free',
                'instructor' => 'Budi Santoso',
            ],
            [
                'id' => 2,
                'title' => 'HTML & CSS Fundamentals',
                'description' => 'Master the building blocks of web development',
                'category' => 'Web Development',
                'level' => 'Pemula',
                'price' => 'Free',
                'rating' => 4.7,
                'students' => 1890,
                'duration' => '6 hours',
                'image' => 'courses/placeholder.svg',
                'type' => 'free',
                'instructor' => '<PERSON><PERSON>',
            ],
            [
                'id' => 3,
                'title' => 'Git & GitHub Basics',
                'description' => 'Version control essentials for developers',
                'category' => 'Development Tools',
                'level' => 'Pemula',
                'price' => 'Free',
                'rating' => 4.5,
                'students' => 1650,
                'duration' => '3 hours',
                'image' => 'courses/placeholder.svg',
                'type' => 'free',
                'instructor' => 'Ahmad Rahman',
            ],
            // Premium Courses
            [
                'id' => 4,
                'title' => 'Complete Web Development Bootcamp',
                'description' => 'From zero to full-stack developer in 12 weeks',
                'category' => 'Web Development',
                'level' => 'Menengah',
                'price' => 'Rp 299.000',
                'original_price' => 'Rp 399.000',
                'rating' => 4.9,
                'students' => 2543,
                'duration' => '40 hours',
                'image' => 'courses/placeholder.svg',
                'type' => 'premium',
                'instructor' => 'Dr. Maya Putri',
            ],
            [
                'id' => 5,
                'title' => 'Data Science with Python',
                'description' => 'Master data analysis and machine learning',
                'category' => 'Data Science',
                'level' => 'Lanjutan',
                'price' => 'Rp 449.000',
                'rating' => 4.8,
                'students' => 1567,
                'duration' => '50 hours',
                'image' => 'courses/placeholder.svg',
                'type' => 'premium',
                'instructor' => 'Dr. Indira Sari',
            ],
            // Tutorial Courses
            [
                'id' => 6,
                'title' => 'JavaScript ES6+ Features',
                'description' => 'Modern JavaScript features explained step by step',
                'category' => 'Programming',
                'level' => 'Menengah',
                'price' => 'Free',
                'rating' => 4.6,
                'students' => 3421,
                'duration' => '8 hours',
                'image' => 'courses/placeholder.svg',
                'type' => 'tutorial',
                'instructor' => 'Andi Pratama',
            ],
            // Webinar Courses
            [
                'id' => 7,
                'title' => 'Tech Trends 2024',
                'description' => 'Explore the latest technology trends and opportunities',
                'category' => 'Technology',
                'level' => 'Semua Level',
                'price' => 'Rp 49.000',
                'rating' => 4.8,
                'students' => 1567,
                'duration' => '2 hours',
                'image' => 'courses/placeholder.svg',
                'type' => 'webinar',
                'instructor' => 'Dr. Ahmad Rahman',
                'is_live' => true,
            ],
        ];

        $testimonials = [
            [
                'id' => 1,
                'name' => 'Andi Pratama',
                'role' => 'Frontend Developer di Gojek',
                'avatar' => 'avatars/placeholder.svg',
                'rating' => 5,
                'content' => 'Ngambiskuy membantu saya menjadi developer Gojek dalam 4 bulan! Jalur pembelajaran bertenaga AI sangat sesuai dengan tujuan saya.',
                'course' => 'Bootcamp Pengembangan Web Lengkap',
            ],
            [
                'id' => 2,
                'name' => 'Sari Dewi',
                'role' => 'Data Scientist di Tokopedia',
                'avatar' => 'avatars/placeholder.svg',
                'rating' => 5,
                'content' => 'Track Data Science melebihi ekspektasi saya. Asisten pengajar AI tersedia 24/7 untuk membantu konsep yang kompleks.',
                'course' => 'Data Science dengan Python',
            ],
            [
                'id' => 3,
                'name' => 'Rizki Hakim',
                'role' => 'Mobile Developer di Bukalapak',
                'avatar' => 'avatars/placeholder.svg',
                'rating' => 5,
                'content' => 'Dari nol menjadi mobile developer dalam 6 bulan. Fitur gamifikasi membuat saya tetap termotivasi.',
                'course' => 'Pengembangan Aplikasi Mobile',
            ],
        ];

        $blogPosts = [
            [
                'id' => 1,
                'title' => 'Cara Menjadi Data Analyst di Indonesia: Panduan Lengkap 2024',
                'excerpt' => 'Temukan roadmap langkah demi langkah untuk menjadi data analyst sukses di industri tech Indonesia yang berkembang pesat.',
                'author' => 'Dr. Sari Wijaya',
                'author_avatar' => 'avatars/placeholder.svg',
                'category' => 'Panduan Karir',
                'read_time' => '8 menit baca',
                'publish_date' => '15 Jan 2024',
                'image' => 'blog/placeholder.svg',
                'featured' => true,
            ],
            [
                'id' => 2,
                'title' => 'Dari Bootcamp ke Gojek: Kisah Sukses Andi',
                'excerpt' => 'Bagaimana Andi Pratama berubah dari pemula total menjadi frontend developer di Gojek hanya dalam 4 bulan.',
                'author' => 'Maya Putri',
                'author_avatar' => 'avatars/placeholder.svg',
                'category' => 'Kisah Sukses',
                'read_time' => '6 menit baca',
                'publish_date' => '12 Jan 2024',
                'image' => 'blog/placeholder.svg',
                'featured' => false,
            ],
            [
                'id' => 3,
                'title' => '10 Bahasa Pemrograman Terpopuler untuk Dipelajari di 2024',
                'excerpt' => 'Jelajahi bahasa pemrograman yang paling diminati yang akan meningkatkan prospek karir Anda di tahun 2024.',
                'author' => 'Budi Santoso',
                'author_avatar' => 'avatars/placeholder.svg',
                'category' => 'Tren Tech',
                'read_time' => '10 menit baca',
                'publish_date' => '10 Jan 2024',
                'image' => 'blog/placeholder.svg',
                'featured' => false,
            ],
        ];

        $examStats = [
            'total_exams_taken' => 50000,
            'available_exams' => 1200,
            'certificates_issued' => 15000,
            'satisfaction_rate' => 98,
        ];

        return view('home', compact('courses', 'testimonials', 'blogPosts', 'examStats'));
    }
}