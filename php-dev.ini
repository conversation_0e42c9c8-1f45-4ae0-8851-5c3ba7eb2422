; Custom PHP configuration for development with large file uploads
; Use with: php -c php-dev.ini -S 127.0.0.1:8000 server.php

; File upload settings
file_uploads = On
upload_max_filesize = 150M
post_max_size = 150M
max_file_uploads = 20

; Execution time settings
max_execution_time = 600
max_input_time = 600
memory_limit = 512M

; Error reporting for development
display_errors = On
display_startup_errors = On
error_reporting = E_ALL

; Session settings
session.gc_maxlifetime = 1440

; Other useful development settings
log_errors = On
log_errors_max_len = 1024
