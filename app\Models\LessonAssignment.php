<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class LessonAssignment extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'lesson_id',
        'title',
        'description',
        'requirements',
        'allowed_file_types',
        'max_file_size',
        'max_files',
        'due_date',
        'max_points',
        'allow_late_submission',
        'late_penalty_percent',
        'is_published',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'allowed_file_types' => 'array',
        'max_file_size' => 'integer',
        'max_files' => 'integer',
        'due_date' => 'datetime',
        'max_points' => 'integer',
        'allow_late_submission' => 'boolean',
        'late_penalty_percent' => 'integer',
        'is_published' => 'boolean',
    ];

    /**
     * Get the lesson that owns the assignment.
     */
    public function lesson(): BelongsTo
    {
        return $this->belongsTo(CourseLesson::class, 'lesson_id');
    }

    /**
     * Get published assignments.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Check if the assignment is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->due_date && Carbon::now()->isAfter($this->due_date);
    }

    /**
     * Check if late submission is allowed.
     */
    public function allowsLateSubmission(): bool
    {
        return $this->allow_late_submission;
    }

    /**
     * Get the formatted due date.
     */
    public function getFormattedDueDateAttribute(): ?string
    {
        return $this->due_date ? $this->due_date->format('d M Y, H:i') : null;
    }

    /**
     * Get the time remaining until due date.
     */
    public function getTimeRemainingAttribute(): ?string
    {
        if (!$this->due_date) {
            return null;
        }

        $now = Carbon::now();
        if ($now->isAfter($this->due_date)) {
            return 'Overdue';
        }

        return $now->diffForHumans($this->due_date, true);
    }

    /**
     * Get allowed file types as a formatted string.
     */
    public function getAllowedFileTypesStringAttribute(): string
    {
        if (!$this->allowed_file_types) {
            return 'Semua jenis file';
        }

        return implode(', ', $this->allowed_file_types);
    }

    /**
     * Get max file size in human readable format.
     */
    public function getMaxFileSizeFormattedAttribute(): string
    {
        return $this->max_file_size . ' MB';
    }
}
