<?php
/**
 * Storage Test Script
 * Test if storage directories are writable and accessible
 */

echo "<h2>Storage Directory Test</h2>";

// Test storage directories
$directories = [
    'storage/app' => storage_path('app'),
    'storage/app/public' => storage_path('app/public'),
    'storage/logs' => storage_path('logs'),
];

echo "<h3>Directory Permissions:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Directory</th><th>Path</th><th>Exists</th><th>Writable</th><th>Permissions</th></tr>";

foreach ($directories as $name => $path) {
    $exists = is_dir($path) ? '✅' : '❌';
    $writable = is_writable($path) ? '✅' : '❌';
    $permissions = file_exists($path) ? substr(sprintf('%o', fileperms($path)), -4) : 'N/A';
    
    echo "<tr>";
    echo "<td>{$name}</td>";
    echo "<td>{$path}</td>";
    echo "<td>{$exists}</td>";
    echo "<td>{$writable}</td>";
    echo "<td>{$permissions}</td>";
    echo "</tr>";
}

echo "</table>";

// Test creating a test file
echo "<h3>File Creation Test:</h3>";
$testDir = storage_path('app/test');
$testFile = $testDir . '/test.txt';

try {
    // Create directory if it doesn't exist
    if (!is_dir($testDir)) {
        mkdir($testDir, 0755, true);
        echo "✅ Created test directory: {$testDir}<br>";
    } else {
        echo "✅ Test directory exists: {$testDir}<br>";
    }
    
    // Try to write a test file
    file_put_contents($testFile, 'Test content: ' . date('Y-m-d H:i:s'));
    echo "✅ Successfully created test file: {$testFile}<br>";
    
    // Try to read the file
    $content = file_get_contents($testFile);
    echo "✅ Successfully read test file content: {$content}<br>";
    
    // Clean up
    unlink($testFile);
    rmdir($testDir);
    echo "✅ Successfully cleaned up test files<br>";
    
} catch (Exception $e) {
    echo "❌ Error during file test: " . $e->getMessage() . "<br>";
}

// Test Laravel Storage facade
echo "<h3>Laravel Storage Test:</h3>";
try {
    // This requires Laravel to be bootstrapped, so we'll skip it for now
    echo "⚠️ Laravel Storage facade test skipped (requires full Laravel bootstrap)<br>";
} catch (Exception $e) {
    echo "❌ Laravel Storage test failed: " . $e->getMessage() . "<br>";
}

// Show PHP upload settings
echo "<h3>PHP Upload Settings:</h3>";
$uploadSettings = [
    'file_uploads' => ini_get('file_uploads') ? 'On' : 'Off',
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_execution_time' => ini_get('max_execution_time'),
    'max_input_time' => ini_get('max_input_time'),
    'memory_limit' => ini_get('memory_limit'),
    'max_file_uploads' => ini_get('max_file_uploads'),
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
foreach ($uploadSettings as $setting => $value) {
    echo "<tr><td>{$setting}</td><td>{$value}</td></tr>";
}
echo "</table>";

// Show disk space
echo "<h3>Disk Space:</h3>";
$storageSpace = disk_free_space(storage_path());
$totalSpace = disk_total_space(storage_path());
$usedSpace = $totalSpace - $storageSpace;

echo "<p><strong>Storage Path:</strong> " . storage_path() . "</p>";
echo "<p><strong>Free Space:</strong> " . formatBytes($storageSpace) . "</p>";
echo "<p><strong>Used Space:</strong> " . formatBytes($usedSpace) . "</p>";
echo "<p><strong>Total Space:</strong> " . formatBytes($totalSpace) . "</p>";

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}

echo "<p><small>Test completed at: " . date('Y-m-d H:i:s') . "</small></p>";
?>
