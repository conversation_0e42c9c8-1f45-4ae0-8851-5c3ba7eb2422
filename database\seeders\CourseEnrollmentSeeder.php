<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\User;
use Illuminate\Database\Seeder;

class CourseEnrollmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some users and paid courses for testing
        $users = User::where('is_tutor', false)->take(3)->get();
        $paidCourses = Course::where('is_free', false)->take(2)->get();

        if ($users->count() > 0 && $paidCourses->count() > 0) {
            // Create some test enrollments
            foreach ($users as $index => $user) {
                foreach ($paidCourses as $courseIndex => $course) {
                    // Only enroll some users to some courses for testing
                    if (($index + $courseIndex) % 2 === 0) {
                        CourseEnrollment::create([
                            'user_id' => $user->id,
                            'course_id' => $course->id,
                            'status' => 'active',
                            'amount_paid' => $course->price,
                            'payment_method' => 'credit_card',
                            'payment_reference' => 'TEST_' . time() . '_' . $index . $courseIndex,
                            'enrolled_at' => now(),
                        ]);
                    }
                }
            }
        }
    }
}
