@extends('layouts.user')

@section('title', 'Progress Belajar - Ngambiskuy')

@section('content')
<div class="p-6">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Progress Belajar</h1>
                <p class="text-gray-600 mt-1">Pantau kemajuan dan pencapaian belajar <PERSON></p>
            </div>
        </div>
    </div>

    <!-- Overall Progress -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">Progress Keseluruhan</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="relative w-24 h-24 mx-auto mb-4">
                        <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                            <circle cx="50" cy="50" r="40" stroke="#e5e7eb" stroke-width="8" fill="none"/>
                            <circle cx="50" cy="50" r="40" stroke="#f97316" stroke-width="8" fill="none"
                                    stroke-dasharray="251.2" stroke-dashoffset="{{ 251.2 - (251.2 * $progressData['overall_progress'] / 100) }}"
                                    stroke-linecap="round"/>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-xl font-bold text-gray-900">{{ $progressData['overall_progress'] }}%</span>
                        </div>
                    </div>
                    <h3 class="font-semibold text-gray-900">Progress Total</h3>
                    <p class="text-sm text-gray-600">Dari semua kursus</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900">{{ count($progressData['courses_in_progress']) }}</h3>
                    <p class="text-sm text-gray-600">Kursus Aktif</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900">{{ $progressData['completed_modules'] }}</h3>
                    <p class="text-sm text-gray-600">Modul Selesai</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900">0</h3>
                    <p class="text-sm text-gray-600">Poin XP</p>
                </div>
            </div>
        </div>

        @if(count($progressData['courses_in_progress']) > 0)
            <!-- Courses in Progress -->
            <div class="bg-white rounded-xl shadow-sm p-8 mb-8">
                <h2 class="text-xl font-bold text-gray-900 mb-6">Kursus Sedang Berjalan</h2>
                <div class="space-y-6">
                    @foreach($progressData['courses_in_progress'] as $course)
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">{{ $course['title'] }}</h3>
                                    <p class="text-gray-600">{{ $course['instructor'] }}</p>
                                </div>
                                <div class="text-right">
                                    <span class="text-2xl font-bold text-primary">{{ $course['progress'] }}%</span>
                                    <p class="text-sm text-gray-600">{{ $course['completed_modules'] }}/{{ $course['total_modules'] }} modul</p>
                                </div>
                            </div>
                            <div class="mb-4">
                                <div class="flex justify-between text-sm text-gray-600 mb-1">
                                    <span>Progress</span>
                                    <span>{{ $course['progress'] }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-3">
                                    <div class="bg-primary h-3 rounded-full transition-all duration-300" style="width: {{ $course['progress'] }}%"></div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4 text-sm text-gray-600">
                                    <span>Waktu belajar: {{ $course['time_spent'] }}</span>
                                    <span>Terakhir akses: {{ $course['last_accessed'] }}</span>
                                </div>
                                <button class="btn btn-primary">
                                    Lanjutkan
                                </button>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Learning Path -->
        <div class="bg-white rounded-xl shadow-sm p-8 mb-8">
            <h2 class="text-xl font-bold text-gray-900 mb-6">Jalur Pembelajaran AI</h2>
            @if(count($progressData['learning_path']) > 0)
                <div class="space-y-4">
                    @foreach($progressData['learning_path'] as $index => $step)
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full {{ $step['completed'] ? 'bg-green-500' : ($step['current'] ? 'bg-primary' : 'bg-gray-300') }} flex items-center justify-center">
                                @if($step['completed'])
                                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                @else
                                    <span class="text-white text-sm font-medium">{{ $index + 1 }}</span>
                                @endif
                            </div>
                            <div class="ml-4 flex-1">
                                <h3 class="text-lg font-medium {{ $step['completed'] ? 'text-green-600' : ($step['current'] ? 'text-primary' : 'text-gray-900') }}">
                                    {{ $step['title'] }}
                                </h3>
                                <p class="text-gray-600">{{ $step['description'] }}</p>
                                @if($step['current'])
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary text-white mt-1">
                                        Sedang Berjalan
                                    </span>
                                @endif
                            </div>
                            @if($index < count($progressData['learning_path']) - 1)
                                <div class="absolute left-4 mt-8 w-0.5 h-8 {{ $step['completed'] ? 'bg-green-500' : 'bg-gray-300' }}"></div>
                            @endif
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Jalur Pembelajaran Belum Dimulai</h3>
                    <p class="text-gray-600 mb-6">AI akan membuat jalur pembelajaran yang dipersonalisasi setelah Anda mulai mengikuti kursus</p>
                    <a href="{{ route('user.courses') }}" class="btn btn-primary">
                        Mulai Belajar
                    </a>
                </div>
            @endif
        </div>

    <!-- Achievement & Badges -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">Pencapaian & Badge</h2>
        <div class="text-center py-8">
            <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                </svg>
            </div>
            <h3 class="text-sm font-medium text-gray-900 mb-2">Belum Ada Pencapaian</h3>
            <p class="text-sm text-gray-600 mb-4">Mulai belajar untuk mendapatkan badge dan pencapaian pertama Anda!</p>
            <a href="{{ route('user.courses') }}" class="btn btn-primary">
                Mulai Belajar
            </a>
        </div>
    </div>
</div>
@endsection
