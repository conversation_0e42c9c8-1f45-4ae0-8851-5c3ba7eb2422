<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class QuizQuestion extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'quiz_id',
        'question',
        'type',
        'points',
        'sort_order',
        'explanation',
        'is_required',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'points' => 'integer',
        'sort_order' => 'integer',
        'is_required' => 'boolean',
    ];

    /**
     * Get the quiz that owns the question.
     */
    public function quiz(): BelongsTo
    {
        return $this->belongsTo(LessonQuiz::class, 'quiz_id');
    }

    /**
     * Get the options for the question.
     */
    public function options(): HasMany
    {
        return $this->hasMany(QuizQuestionOption::class, 'question_id')->orderBy('sort_order');
    }

    /**
     * Get the correct options for the question.
     */
    public function correctOptions(): HasMany
    {
        return $this->hasMany(QuizQuestionOption::class, 'question_id')
                    ->where('is_correct', true)
                    ->orderBy('sort_order');
    }

    /**
     * Get the question type in Indonesian.
     */
    public function getTypeIndonesianAttribute(): string
    {
        return match($this->type) {
            'multiple_choice' => 'Pilihan Ganda',
            'true_false' => 'Benar/Salah',
            'short_answer' => 'Jawaban Singkat',
            'essay' => 'Essay',
            default => 'Tidak Diketahui'
        };
    }

    /**
     * Check if this is a multiple choice question.
     */
    public function isMultipleChoice(): bool
    {
        return $this->type === 'multiple_choice';
    }

    /**
     * Check if this is a true/false question.
     */
    public function isTrueFalse(): bool
    {
        return $this->type === 'true_false';
    }

    /**
     * Check if this is a short answer question.
     */
    public function isShortAnswer(): bool
    {
        return $this->type === 'short_answer';
    }

    /**
     * Check if this is an essay question.
     */
    public function isEssay(): bool
    {
        return $this->type === 'essay';
    }
}
