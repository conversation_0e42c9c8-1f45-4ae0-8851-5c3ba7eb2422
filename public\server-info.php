<?php
/**
 * Server Information and Diagnostics
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html>
<head>
    <title>Server Information</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .good { color: green; font-weight: bold; }
        .bad { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
    </style>
</head>
<body>
    <h1>Server Information & Diagnostics</h1>
    
    <div class="section">
        <h2>Basic Server Info</h2>
        <table>
            <tr><th>Property</th><th>Value</th></tr>
            <tr><td>PHP Version</td><td><?= PHP_VERSION ?></td></tr>
            <tr><td>Server Software</td><td><?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></td></tr>
            <tr><td>Document Root</td><td><?= $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown' ?></td></tr>
            <tr><td>Script Name</td><td><?= $_SERVER['SCRIPT_NAME'] ?? 'Unknown' ?></td></tr>
            <tr><td>Request Method</td><td><?= $_SERVER['REQUEST_METHOD'] ?? 'Unknown' ?></td></tr>
            <tr><td>Content Length</td><td><?= $_SERVER['CONTENT_LENGTH'] ?? 'Not set' ?></td></tr>
        </table>
    </div>
    
    <div class="section">
        <h2>PHP Upload Configuration</h2>
        <?php
        $uploadSettings = [
            'file_uploads' => ini_get('file_uploads'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'max_execution_time' => ini_get('max_execution_time'),
            'max_input_time' => ini_get('max_input_time'),
            'memory_limit' => ini_get('memory_limit'),
            'max_file_uploads' => ini_get('max_file_uploads'),
            'upload_tmp_dir' => ini_get('upload_tmp_dir') ?: sys_get_temp_dir(),
        ];
        
        function parseSize($size) {
            $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
            $size = preg_replace('/[^0-9\.]/', '', $size);
            if ($unit) {
                return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
            } else {
                return round($size);
            }
        }
        
        $uploadMaxBytes = parseSize($uploadSettings['upload_max_filesize']);
        $postMaxBytes = parseSize($uploadSettings['post_max_size']);
        $effectiveLimit = min($uploadMaxBytes, $postMaxBytes);
        ?>
        
        <table>
            <tr><th>Setting</th><th>Value</th><th>Status</th></tr>
            <?php foreach ($uploadSettings as $setting => $value): ?>
                <?php
                $status = '';
                if ($setting === 'file_uploads') {
                    $status = $value ? '<span class="good">✅ Enabled</span>' : '<span class="bad">❌ Disabled</span>';
                } elseif ($setting === 'upload_max_filesize') {
                    $status = $uploadMaxBytes >= (100 * 1024 * 1024) ? '<span class="good">✅ Good</span>' : '<span class="warning">⚠️ Too small</span>';
                } elseif ($setting === 'post_max_size') {
                    $status = $postMaxBytes >= (100 * 1024 * 1024) ? '<span class="good">✅ Good</span>' : '<span class="warning">⚠️ Too small</span>';
                }
                ?>
                <tr>
                    <td><?= $setting ?></td>
                    <td><?= $value ?: 'Not set' ?></td>
                    <td><?= $status ?></td>
                </tr>
            <?php endforeach; ?>
        </table>
        
        <p><strong>Effective Upload Limit:</strong> <?= formatBytes($effectiveLimit) ?></p>
    </div>
    
    <div class="section">
        <h2>Directory Permissions</h2>
        <?php
        $directories = [
            'Current Directory' => __DIR__,
            'Storage App' => dirname(__DIR__) . '/storage/app',
            'Storage Logs' => dirname(__DIR__) . '/storage/logs',
            'Temp Directory' => sys_get_temp_dir(),
        ];
        ?>
        
        <table>
            <tr><th>Directory</th><th>Path</th><th>Exists</th><th>Writable</th><th>Permissions</th></tr>
            <?php foreach ($directories as $name => $path): ?>
                <tr>
                    <td><?= $name ?></td>
                    <td><?= $path ?></td>
                    <td><?= is_dir($path) ? '<span class="good">✅</span>' : '<span class="bad">❌</span>' ?></td>
                    <td><?= is_writable($path) ? '<span class="good">✅</span>' : '<span class="bad">❌</span>' ?></td>
                    <td><?= file_exists($path) ? substr(sprintf('%o', fileperms($path)), -4) : 'N/A' ?></td>
                </tr>
            <?php endforeach; ?>
        </table>
    </div>
    
    <div class="section">
        <h2>Disk Space</h2>
        <?php
        $storageSpace = disk_free_space(__DIR__);
        $totalSpace = disk_total_space(__DIR__);
        $usedSpace = $totalSpace - $storageSpace;
        ?>
        
        <table>
            <tr><th>Metric</th><th>Value</th></tr>
            <tr><td>Free Space</td><td><?= formatBytes($storageSpace) ?></td></tr>
            <tr><td>Used Space</td><td><?= formatBytes($usedSpace) ?></td></tr>
            <tr><td>Total Space</td><td><?= formatBytes($totalSpace) ?></td></tr>
            <tr><td>Usage %</td><td><?= round(($usedSpace / $totalSpace) * 100, 2) ?>%</td></tr>
        </table>
    </div>
    
    <div class="section">
        <h2>Loaded PHP Extensions</h2>
        <?php
        $importantExtensions = ['fileinfo', 'gd', 'curl', 'openssl', 'zip', 'mbstring'];
        ?>
        <table>
            <tr><th>Extension</th><th>Status</th></tr>
            <?php foreach ($importantExtensions as $ext): ?>
                <tr>
                    <td><?= $ext ?></td>
                    <td><?= extension_loaded($ext) ? '<span class="good">✅ Loaded</span>' : '<span class="bad">❌ Not loaded</span>' ?></td>
                </tr>
            <?php endforeach; ?>
        </table>
    </div>
    
    <div class="section">
        <h2>Recommendations</h2>
        <ul>
            <?php if (!ini_get('file_uploads')): ?>
                <li class="bad">❌ Enable file uploads in PHP configuration</li>
            <?php endif; ?>
            
            <?php if ($uploadMaxBytes < (100 * 1024 * 1024)): ?>
                <li class="warning">⚠️ Increase upload_max_filesize to at least 100M</li>
            <?php endif; ?>
            
            <?php if ($postMaxBytes < (100 * 1024 * 1024)): ?>
                <li class="warning">⚠️ Increase post_max_size to at least 100M</li>
            <?php endif; ?>
            
            <?php if ($effectiveLimit >= (100 * 1024 * 1024)): ?>
                <li class="good">✅ Upload limits are sufficient for 100MB files</li>
            <?php endif; ?>
        </ul>
    </div>
    
    <?php
    function formatBytes($size, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        return round($size, $precision) . ' ' . $units[$i];
    }
    ?>
    
    <p><small>Generated at: <?= date('Y-m-d H:i:s') ?></small></p>
</body>
</html>
