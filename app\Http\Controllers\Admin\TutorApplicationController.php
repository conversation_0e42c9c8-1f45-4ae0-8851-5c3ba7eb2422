<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TutorProfile;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class TutorApplicationController extends Controller
{
    /**
     * Display a listing of tutor applications.
     */
    public function index(Request $request)
    {
        $status = $request->get('status', 'all');

        $query = TutorProfile::with('user')
            ->orderBy('submitted_at', 'desc');

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        $applications = $query->paginate(20);

        $statusCounts = [
            'all' => TutorProfile::count(),
            'submitted' => TutorProfile::where('status', 'submitted')->count(),
            'under_review' => TutorProfile::where('status', 'under_review')->count(),
            'approved' => TutorProfile::where('status', 'approved')->count(),
            'rejected' => TutorProfile::where('status', 'rejected')->count(),
        ];

        return view('admin.tutor-applications.index', compact('applications', 'statusCounts', 'status'));
    }

    /**
     * Display the specified tutor application.
     */
    public function show(TutorProfile $application)
    {
        $application->load('user', 'reviewer');

        return view('admin.tutor-applications.show', compact('application'));
    }

    /**
     * Update the application status to under review.
     */
    public function review(TutorProfile $application)
    {
        $application->update([
            'status' => 'under_review',
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
        ]);

        return back()->with('success', 'Aplikasi telah ditandai sedang ditinjau.');
    }

    /**
     * Approve the tutor application.
     */
    public function approve(TutorProfile $application)
    {
        $application->update([
            'status' => 'approved',
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
            'rejection_reason' => null,
        ]);

        // Update user status
        $application->user->update([
            'is_tutor' => true,
            'tutor_status' => 'approved',
        ]);

        return back()->with('success', 'Aplikasi tutor telah disetujui.');
    }

    /**
     * Reject the tutor application.
     */
    public function reject(Request $request, TutorProfile $application)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ], [
            'rejection_reason.required' => 'Alasan penolakan wajib diisi.',
            'rejection_reason.max' => 'Alasan penolakan maksimal 1000 karakter.',
        ]);

        $application->update([
            'status' => 'rejected',
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
            'rejection_reason' => $request->rejection_reason,
        ]);

        // Update user status
        $application->user->update([
            'is_tutor' => false,
            'tutor_status' => 'rejected',
        ]);

        return back()->with('success', 'Aplikasi tutor telah ditolak.');
    }

    /**
     * Download uploaded file.
     */
    public function downloadFile(TutorProfile $application, $type)
    {
        $filePath = null;
        $fileName = null;

        switch ($type) {
            case 'identity':
                $filePath = $application->identity_photo_path;
                $fileName = 'identity_' . $application->user->name . '_' . $application->identity_number;
                break;
            case 'portfolio':
                $filePath = $application->portfolio_path;
                $fileName = 'portfolio_' . $application->user->name;
                break;
            case 'npwp':
                $filePath = $application->npwp_photo_path;
                $fileName = 'npwp_' . $application->user->name . '_' . $application->npwp_number;
                break;
        }

        if (!$filePath || !Storage::disk('public')->exists($filePath)) {
            abort(404, 'File tidak ditemukan.');
        }

        return response()->download(storage_path('app/public/' . $filePath), $fileName);
    }
}
