<?php $__env->startSection('title', 'Kurikulum - ' . $course->title); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Kurikulum Kursus</h1>
                <p class="text-gray-600 mt-1"><?php echo e($course->title); ?></p>
                <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                        <?php echo e($course->category->name); ?>

                    </span>
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <?php echo e($course->level_indonesian); ?>

                    </span>
                    <span class="flex items-center">
                        <?php if($course->is_free): ?>
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">GRATIS</span>
                        <?php else: ?>
                            <span class="bg-primary text-white text-xs px-2 py-1 rounded"><?php echo e($course->formatted_price); ?></span>
                        <?php endif; ?>
                    </span>
                </div>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('tutor.courses')); ?>" class="btn btn-outline">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Kembali ke Kursus
                </a>
                <button onclick="openAddChapterModal()" class="btn btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Tambah Bab
                </button>
            </div>
        </div>
    </div>

    <!-- Course Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Bab</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($course->chapters->count()); ?></p>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Materi</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($course->lessons->count()); ?></p>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Durasi</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($course->lessons->sum('duration_minutes')); ?> min</p>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Materi Published</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($course->lessons->where('is_published', true)->count()); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Curriculum Content -->
    <div class="bg-white rounded-lg shadow-sm">
        <?php if($course->chapters->count() > 0): ?>
            <div class="divide-y divide-gray-200">
                <?php $__currentLoopData = $course->chapters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chapter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="p-6">
                        <!-- Chapter Header -->
                        <div class="flex items-center justify-between mb-4 group">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-gradient-to-br from-primary to-blue-600 text-white rounded-xl flex items-center justify-center text-sm font-bold shadow-lg group-hover:shadow-xl transition-all duration-200">
                                        <?php echo e($loop->iteration); ?>

                                    </div>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900"><?php echo e($chapter->title); ?></h3>
                                    <?php if($chapter->description): ?>
                                        <p class="text-sm text-gray-600 mt-1"><?php echo e($chapter->description); ?></p>
                                    <?php endif; ?>
                                    <div class="flex items-center space-x-4 mt-2">
                                        <span class="text-xs text-gray-500"><?php echo e($chapter->lessons->count()); ?> materi</span>
                                        <span class="text-xs text-gray-500"><?php echo e($chapter->lessons->sum('duration_minutes')); ?> menit</span>
                                        <?php if($chapter->is_published): ?>
                                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Published</span>
                                        <?php else: ?>
                                            <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">Draft</span>
                                        <?php endif; ?>
                                        <?php if($chapter->is_free): ?>
                                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">Gratis</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <a href="<?php echo e(route('tutor.curriculum.create-material', [$course, $chapter])); ?>"
                                        class="px-3 py-2 text-sm font-medium text-green-600 bg-green-50 hover:bg-green-100 rounded-lg transition-all duration-200 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Tambah Materi
                                </a>
                                <button onclick="editChapter('<?php echo e($chapter->id); ?>', '<?php echo e($chapter->title); ?>', '<?php echo e($chapter->description); ?>', <?php echo e($chapter->is_published ? 'true' : 'false'); ?>, <?php echo e($chapter->is_free ? 'true' : 'false'); ?>)"
                                        class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                                        title="Edit Bab">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </button>
                                <button onclick="deleteChapter('<?php echo e($chapter->id); ?>', '<?php echo e($chapter->title); ?>')"
                                        class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200"
                                        title="Hapus Bab">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Chapter Lessons -->
                        <?php if($chapter->lessons->count() > 0): ?>
                            <div class="ml-11 space-y-3">
                                <?php $__currentLoopData = $chapter->lessons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lesson): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200 hover:shadow-md hover:border-gray-300 transition-all duration-200 group">
                                        <div class="flex items-center space-x-4">
                                            <div class="flex-shrink-0">
                                                <div class="w-8 h-8 rounded-lg flex items-center justify-center
                                                    <?php if($lesson->type === 'video'): ?> bg-red-100 text-red-600
                                                    <?php elseif($lesson->type === 'text'): ?> bg-blue-100 text-blue-600
                                                    <?php elseif($lesson->type === 'quiz'): ?> bg-yellow-100 text-yellow-600
                                                    <?php else: ?> bg-purple-100 text-purple-600
                                                    <?php endif; ?>">
                                                    <?php if($lesson->type === 'video'): ?>
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                        </svg>
                                                    <?php elseif($lesson->type === 'text'): ?>
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                        </svg>
                                                    <?php elseif($lesson->type === 'quiz'): ?>
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                    <?php else: ?>
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                                        </svg>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div>
                                                <h4 class="text-sm font-medium text-gray-900"><?php echo e($lesson->title); ?></h4>
                                                <div class="flex items-center space-x-3 mt-1">
                                                    <span class="text-xs text-gray-500"><?php echo e($lesson->type_indonesian); ?></span>
                                                    <?php if($lesson->duration_minutes > 0): ?>
                                                        <span class="text-xs text-gray-500"><?php echo e($lesson->duration_minutes); ?> menit</span>
                                                    <?php endif; ?>
                                                    <?php if($lesson->is_published): ?>
                                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Published</span>
                                                    <?php else: ?>
                                                        <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">Draft</span>
                                                    <?php endif; ?>
                                                    <?php if($lesson->is_preview && !$course->is_free): ?>
                                                        <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">Preview</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                            <a href="<?php echo e(route('tutor.curriculum.edit-material', [$course, $chapter, $lesson])); ?>"
                                                    class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                                                    title="Edit Materi">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </a>
                                            <button onclick="deleteLesson('<?php echo e($lesson->id); ?>', '<?php echo e($chapter->id); ?>', '<?php echo e($lesson->title); ?>')"
                                                    class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200"
                                                    title="Hapus Materi">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <div class="ml-11 p-4 bg-gray-50 rounded-lg text-center">
                                <p class="text-gray-500 text-sm">Belum ada materi dalam bab ini</p>
                                <a href="<?php echo e(route('tutor.curriculum.create-material', [$course, $chapter])); ?>" class="btn btn-sm btn-primary mt-2">
                                    Tambah Materi Pertama
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <!-- Empty State -->
            <div class="p-12 text-center">
                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Belum Ada Kurikulum</h3>
                <p class="text-gray-600 mb-6">Mulai buat kurikulum kursus Anda dengan menambahkan bab pertama</p>
                <button onclick="openAddChapterModal()" class="btn btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Tambah Bab Pertama
                </button>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add Chapter Modal -->
<div id="addChapterModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50 flex items-center justify-center">
    <div class="relative mx-auto p-0 border-0 w-full max-w-md">
        <div class="bg-white rounded-xl shadow-2xl overflow-hidden">
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-primary to-blue-600 px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-white">Tambah Bab Baru</h3>
                    <button type="button" onclick="closeAddChapterModal()" class="text-white hover:text-gray-200 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <form action="<?php echo e(route('tutor.curriculum.store-chapter', $course)); ?>" method="POST" class="p-6">
                <?php echo csrf_field(); ?>
                <div class="space-y-5">
                    <div>
                        <label for="chapter_title" class="block text-sm font-medium text-gray-700 mb-2">
                            Judul Bab <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="chapter_title" name="title" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                               placeholder="Contoh: Bab 1: Pengenalan Programming">
                    </div>

                    <div>
                        <label for="chapter_description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi</label>
                        <textarea id="chapter_description" name="description" rows="3"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 resize-none"
                                  placeholder="Deskripsi singkat tentang bab ini..."></textarea>
                    </div>

                    <?php if(!$course->is_free): ?>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <label class="flex items-start">
                                <input type="checkbox" name="is_free" value="1" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1">
                                <div class="ml-3">
                                    <span class="text-sm font-medium text-gray-700">Bab Gratis</span>
                                    <p class="text-xs text-gray-500 mt-1">Bab ini dapat diakses tanpa membeli kursus (cocok untuk preview)</p>
                                </div>
                            </label>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Modal Footer -->
                <div class="flex justify-end space-x-3 mt-8 pt-4 border-t border-gray-200">
                    <button type="button" onclick="closeAddChapterModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200">
                        Batal
                    </button>
                    <button type="submit"
                            class="px-6 py-2 text-sm font-medium text-white bg-primary hover:bg-primary-dark rounded-lg transition-colors duration-200 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Tambah Bab
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>




<script>
function openAddChapterModal() {
    document.getElementById('addChapterModal').classList.remove('hidden');
}

function closeAddChapterModal() {
    document.getElementById('addChapterModal').classList.add('hidden');
}



function editChapter(id, title, description, isPublished, isFree) {
    // This will be implemented when we create the edit modal
    alert('Edit chapter: ' + title);
}

function deleteChapter(id, title) {
    if (confirm('Apakah Anda yakin ingin menghapus bab "' + title + '"? Semua materi dalam bab ini juga akan dihapus.')) {
        // Create and submit delete form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo e(route("tutor.curriculum.delete-chapter", [$course, "__CHAPTER_ID__"])); ?>'.replace('__CHAPTER_ID__', id);

        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';

        const tokenField = document.createElement('input');
        tokenField.type = 'hidden';
        tokenField.name = '_token';
        tokenField.value = '<?php echo e(csrf_token()); ?>';

        form.appendChild(methodField);
        form.appendChild(tokenField);
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteLesson(lessonId, chapterId, title) {
    if (confirm('Apakah Anda yakin ingin menghapus materi "' + title + '"?')) {
        // Create and submit delete form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo e(route("tutor.curriculum.delete-lesson", [$course, "__CHAPTER_ID__", "__LESSON_ID__"])); ?>'.replace('__CHAPTER_ID__', chapterId).replace('__LESSON_ID__', lessonId);

        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';

        const tokenField = document.createElement('input');
        tokenField.type = 'hidden';
        tokenField.name = '_token';
        tokenField.value = '<?php echo e(csrf_token()); ?>';

        form.appendChild(methodField);
        form.appendChild(tokenField);
        document.body.appendChild(form);
        form.submit();
    }
}

// Close modals when clicking outside
document.getElementById('addChapterModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeAddChapterModal();
    }
});

// Close modals with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeAddChapterModal();
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.tutor', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuy\resources\views/tutor/curriculum/index.blade.php ENDPATH**/ ?>