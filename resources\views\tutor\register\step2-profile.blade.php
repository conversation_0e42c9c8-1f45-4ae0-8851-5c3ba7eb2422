@extends('layouts.app')

@section('title', 'Daftar Sebagai Tutor - Buat Profil')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Progress Steps -->
        <div class="mb-8">
            <div class="flex items-center justify-center">
                <div class="flex items-center space-x-4">
                    <!-- Step 1 - Completed -->
                    <div class="flex items-center">
                        <div class="flex items-center justify-center w-10 h-10 bg-green-500 text-white rounded-full">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <span class="ml-2 text-sm font-medium text-green-600">Syarat & Ketentuan</span>
                    </div>

                    <!-- Connector -->
                    <div class="w-16 h-1 bg-green-500"></div>

                    <!-- Step 2 - Active -->
                    <div class="flex items-center">
                        <div class="flex items-center justify-center w-10 h-10 bg-primary text-white rounded-full font-semibold">
                            2
                        </div>
                        <span class="ml-2 text-sm font-medium text-primary">Buat Profil</span>
                    </div>

                    <!-- Connector -->
                    <div class="w-16 h-1 bg-gray-300"></div>

                    <!-- Step 3 - Inactive -->
                    <div class="flex items-center">
                        <div class="flex items-center justify-center w-10 h-10 bg-gray-300 text-gray-600 rounded-full font-semibold">
                            3
                        </div>
                        <span class="ml-2 text-sm font-medium text-gray-500">Review & Submit</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="bg-white rounded-lg shadow-sm border p-8">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Buat Profil Kamu</h1>
                <p class="text-gray-600">Lengkapi identitas dirimu terlebih dahulu!</p>
            </div>

            <!-- Error Summary -->
            @if ($errors->any())
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Terdapat {{ $errors->count() }} kesalahan yang perlu diperbaiki:
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc list-inside space-y-1">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <form action="{{ route('tutor.register.profile.process') }}" method="POST" enctype="multipart/form-data" class="space-y-8">
                @csrf

                <!-- Personal Identity Section -->
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Identitas Diri</h2>

                    <!-- Profile Picture -->
                    <div class="mb-8">
                        <label class="block text-sm font-medium text-gray-700 mb-4">
                            Foto Profil
                        </label>
                        <div class="flex items-center space-x-6">
                            <div class="shrink-0">
                                @if(auth()->user()->profile_picture)
                                    <img class="h-20 w-20 object-cover rounded-full border-2 border-gray-300"
                                         src="{{ auth()->user()->getProfilePictureUrl() }}"
                                         alt="Profile picture">
                                @else
                                    <div class="h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center border-2 border-gray-300">
                                        <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                    </div>
                                @endif
                            </div>
                            <div class="flex-1">
                                <input type="file"
                                       id="profile_picture"
                                       name="profile_picture"
                                       accept="image/jpeg,image/png,image/jpg"
                                       class="input w-full @error('profile_picture') border-red-500 @enderror">
                                <p class="text-xs text-gray-500 mt-1">Foto profil yang akan ditampilkan di halaman publik Anda (jpeg, png, jpg max. 2MB)</p>
                                @error('profile_picture')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Full Name -->
                        <div>
                            <label for="full_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Nama Lengkap <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="full_name"
                                   name="full_name"
                                   value="{{ old('full_name', $profile->full_name ?? '') }}"
                                   placeholder="Nama Lengkap"
                                   required
                                   class="input w-full @error('full_name') border-red-500 @enderror">
                            @error('full_name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Public Name -->
                        <div>
                            <label for="public_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Nama Publik <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="public_name"
                                   name="public_name"
                                   value="{{ old('public_name', $profile->public_name ?? '') }}"
                                   placeholder="Nama yang akan ditampilkan di kursus Anda"
                                   required
                                   class="input w-full @error('public_name') border-red-500 @enderror">
                            <div class="flex items-center justify-between mt-1">
                                <p class="text-xs text-gray-500">Nama ini akan muncul di profil publik dan kursus Anda. Bisa sama dengan nama lengkap atau nama panggilan/komunitas.</p>
                                <button type="button"
                                        id="copy-full-name"
                                        class="text-xs text-primary hover:text-primary-dark font-medium">
                                    Salin dari nama lengkap
                                </button>
                            </div>
                            @error('public_name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Identity Type -->
                        <div>
                            <label for="identity_type" class="block text-sm font-medium text-gray-700 mb-2">
                                Jenis Identitas <span class="text-red-500">*</span>
                            </label>
                            <select id="identity_type"
                                    name="identity_type"
                                    required
                                    class="select w-full @error('identity_type') border-red-500 @enderror">
                                <option value="">Pilih Jenis Identitas</option>
                                @foreach($identityTypes as $key => $label)
                                    <option value="{{ $key }}" {{ old('identity_type', $profile->identity_type ?? '') == $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                            @error('identity_type')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Identity Number -->
                        <div>
                            <label for="identity_number" class="block text-sm font-medium text-gray-700 mb-2">
                                Nomor Identitas <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="identity_number"
                                   name="identity_number"
                                   value="{{ old('identity_number', $profile->identity_number ?? '') }}"
                                   placeholder="Nomor Identitas"
                                   required
                                   class="input w-full @error('identity_number') border-red-500 @enderror">
                            @error('identity_number')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Identity Photo -->
                        <div>
                            <label for="identity_photo" class="block text-sm font-medium text-gray-700 mb-2">
                                Unggah Foto (KTP/SIM/Passport) (jpeg,png,jpg max. 2048kb) <span class="text-red-500">*</span>
                            </label>
                            <input type="file"
                                   id="identity_photo"
                                   name="identity_photo"
                                   accept="image/jpeg,image/png,image/jpg"
                                   class="input w-full @error('identity_photo') border-red-500 @enderror">
                            @if($profile && $profile->identity_photo_path)
                                <p class="text-sm text-green-600 mt-1">File sudah diunggah</p>
                            @endif
                            @error('identity_photo')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Bank Name -->
                        <div>
                            <label for="bank_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Nama Bank <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="bank_name"
                                   name="bank_name"
                                   value="{{ old('bank_name', $profile->bank_name ?? '') }}"
                                   placeholder="Nama Bank"
                                   required
                                   class="input w-full @error('bank_name') border-red-500 @enderror">
                            @error('bank_name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Bank Account Number -->
                        <div>
                            <label for="bank_account_number" class="block text-sm font-medium text-gray-700 mb-2">
                                Nomor Rekening <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="bank_account_number"
                                   name="bank_account_number"
                                   value="{{ old('bank_account_number', $profile->bank_account_number ?? '') }}"
                                   placeholder="Nomor Rekening"
                                   required
                                   class="input w-full @error('bank_account_number') border-red-500 @enderror">
                            @error('bank_account_number')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Education Level -->
                        <div>
                            <label for="education_level" class="block text-sm font-medium text-gray-700 mb-2">
                                Pendidikan Terakhir <span class="text-red-500">*</span>
                            </label>
                            <select id="education_level"
                                    name="education_level"
                                    required
                                    class="select w-full @error('education_level') border-red-500 @enderror">
                                <option value="">Pilih Pendidikan Terlebih Dahulu</option>
                                @foreach($educationLevels as $key => $label)
                                    <option value="{{ $key }}" {{ old('education_level', $profile->education_level ?? '') == $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                            @error('education_level')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Phone Number -->
                        <div>
                            <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-2">
                                Nomor Telp/Hp <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="phone_number"
                                   name="phone_number"
                                   value="{{ old('phone_number', $profile->phone_number ?? '') }}"
                                   placeholder="Nomor Whatsapp yang dapat dihubungi"
                                   required
                                   class="input w-full @error('phone_number') border-red-500 @enderror">
                            @error('phone_number')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Informasi Tambahan</h2>
                    <p class="text-sm text-gray-600 mb-4">Kamu boleh melengkapi form dibawah jika berkenan</p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Portfolio -->
                        <div>
                            <label for="portfolio" class="block text-sm font-medium text-gray-700 mb-2">
                                Portofolio atau Resume (PDF) max. 2048kb
                            </label>
                            <input type="file"
                                   id="portfolio"
                                   name="portfolio"
                                   accept="application/pdf"
                                   class="input w-full @error('portfolio') border-red-500 @enderror">
                            @if($profile && $profile->portfolio_path)
                                <p class="text-sm text-green-600 mt-1">File sudah diunggah</p>
                            @endif
                            @error('portfolio')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- NPWP Number -->
                        <div>
                            <label for="npwp_number" class="block text-sm font-medium text-gray-700 mb-2">
                                Nomor NPWP
                            </label>
                            <input type="text"
                                   id="npwp_number"
                                   name="npwp_number"
                                   value="{{ old('npwp_number', $profile->npwp_number ?? '') }}"
                                   placeholder="Nomor NPWP"
                                   class="input w-full @error('npwp_number') border-red-500 @enderror">
                            @error('npwp_number')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- NPWP Photo -->
                        <div>
                            <label for="npwp_photo" class="block text-sm font-medium text-gray-700 mb-2">
                                Unggah Foto NPWP (jpeg,png,jpg max. 2048kb)
                            </label>
                            <input type="file"
                                   id="npwp_photo"
                                   name="npwp_photo"
                                   accept="image/jpeg,image/png,image/jpg"
                                   class="input w-full @error('npwp_photo') border-red-500 @enderror">
                            @if($profile && $profile->npwp_photo_path)
                                <p class="text-sm text-green-600 mt-1">File sudah diunggah</p>
                            @endif
                            @error('npwp_photo')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mt-6">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            Deskripsi Singkat
                        </label>
                        <textarea id="description"
                                  name="description"
                                  rows="4"
                                  placeholder="Ceritakan sedikit tentang diri Anda dan keahlian yang dimiliki..."
                                  class="input w-full @error('description') border-red-500 @enderror">{{ old('description', $profile->description ?? '') }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Deskripsi singkat yang akan muncul di kartu profil dan hasil pencarian (maksimal 1000 karakter)</p>
                        @error('description')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Long Description -->
                    <div class="mt-6">
                        <label for="long_description" class="block text-sm font-medium text-gray-700 mb-2">
                            Tentang Saya (Deskripsi Lengkap)
                        </label>
                        <textarea id="long_description"
                                  name="long_description"
                                  rows="8"
                                  placeholder="Ceritakan secara detail tentang latar belakang, pengalaman mengajar, metodologi pembelajaran, keahlian khusus, dan hal-hal yang membuat Anda unik sebagai pengajar. Bagian ini akan ditampilkan di halaman profil publik Anda seperti di Udemy..."
                                  class="input w-full @error('long_description') border-red-500 @enderror">{{ old('long_description', $profile->long_description ?? '') }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Deskripsi lengkap untuk halaman profil publik Anda. Jelaskan pengalaman, metodologi mengajar, dan keahlian khusus (maksimal 10.000 karakter)</p>
                        @error('long_description')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-between pt-6">
                    <a href="{{ route('tutor.register.terms') }}"
                       class="btn btn-outline px-6 py-3">
                        Kembali
                    </a>

                    <button type="submit"
                            class="btn btn-primary px-8 py-3">
                        Lanjutkan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const copyButton = document.getElementById('copy-full-name');
    const fullNameInput = document.getElementById('full_name');
    const publicNameInput = document.getElementById('public_name');

    if (copyButton && fullNameInput && publicNameInput) {
        copyButton.addEventListener('click', function() {
            const fullName = fullNameInput.value.trim();
            if (fullName) {
                publicNameInput.value = fullName;
                publicNameInput.focus();
            } else {
                alert('Silakan isi nama lengkap terlebih dahulu');
                fullNameInput.focus();
            }
        });
    }

    // Auto-focus on first error field if there are validation errors
    @if ($errors->any())
        const firstErrorField = document.querySelector('.border-red-500');
        if (firstErrorField) {
            firstErrorField.focus();
            firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    @endif

    // Form validation before submit
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('input[required], select[required]');
            let hasErrors = false;
            let firstErrorField = null;

            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    field.classList.add('border-red-500');
                    hasErrors = true;
                    if (!firstErrorField) {
                        firstErrorField = field;
                    }
                } else {
                    field.classList.remove('border-red-500');
                }
            });

            if (hasErrors) {
                e.preventDefault();
                if (firstErrorField) {
                    firstErrorField.focus();
                    firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }

                // Show a user-friendly message
                const errorMessage = document.createElement('div');
                errorMessage.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
                errorMessage.innerHTML = `
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                        <span>Mohon lengkapi semua field yang wajib diisi</span>
                        <button type="button" class="ml-2 text-red-500 hover:text-red-700" onclick="this.parentElement.parentElement.remove()">×</button>
                    </div>
                `;
                document.body.appendChild(errorMessage);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (errorMessage.parentElement) {
                        errorMessage.remove();
                    }
                }, 5000);
            }
        });
    }
});
</script>
@endpush

@endsection
