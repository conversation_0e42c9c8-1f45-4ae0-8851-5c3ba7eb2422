<?php
/**
 * Direct PHP Upload Test
 * Test file uploads without <PERSON><PERSON> to isolate server issues
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set upload limits at runtime
ini_set('upload_max_filesize', '150M');
ini_set('post_max_size', '150M');
ini_set('max_execution_time', '600');
ini_set('max_input_time', '600');
ini_set('memory_limit', '512M');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Direct Upload Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .upload-area { border: 2px dashed #ccc; padding: 20px; text-align: center; margin: 20px 0; }
        .result { margin: 20px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Direct PHP Upload Test</h1>
    
    <?php
    // Show current PHP settings
    echo "<div class='info'>";
    echo "<h3>Current PHP Upload Settings:</h3>";
    echo "<table>";
    echo "<tr><th>Setting</th><th>Value</th></tr>";
    echo "<tr><td>upload_max_filesize</td><td>" . ini_get('upload_max_filesize') . "</td></tr>";
    echo "<tr><td>post_max_size</td><td>" . ini_get('post_max_size') . "</td></tr>";
    echo "<tr><td>max_execution_time</td><td>" . ini_get('max_execution_time') . "</td></tr>";
    echo "<tr><td>max_input_time</td><td>" . ini_get('max_input_time') . "</td></tr>";
    echo "<tr><td>memory_limit</td><td>" . ini_get('memory_limit') . "</td></tr>";
    echo "<tr><td>file_uploads</td><td>" . (ini_get('file_uploads') ? 'On' : 'Off') . "</td></tr>";
    echo "</table>";
    echo "</div>";
    
    // Process upload if form was submitted
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        echo "<div class='result'>";
        
        // Check for upload errors
        if (isset($_FILES['test_file'])) {
            $file = $_FILES['test_file'];
            
            echo "<h3>Upload Results:</h3>";
            echo "<table>";
            echo "<tr><th>Property</th><th>Value</th></tr>";
            echo "<tr><td>Name</td><td>" . htmlspecialchars($file['name']) . "</td></tr>";
            echo "<tr><td>Size</td><td>" . formatBytes($file['size']) . " (" . $file['size'] . " bytes)</td></tr>";
            echo "<tr><td>Type</td><td>" . htmlspecialchars($file['type']) . "</td></tr>";
            echo "<tr><td>Temp Name</td><td>" . htmlspecialchars($file['tmp_name']) . "</td></tr>";
            echo "<tr><td>Error Code</td><td>" . $file['error'] . " (" . getUploadErrorMessage($file['error']) . ")</td></tr>";
            echo "</table>";
            
            if ($file['error'] === UPLOAD_ERR_OK) {
                // Try to move the file
                $uploadDir = '../storage/app/test-uploads/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                $fileName = time() . '_' . $file['name'];
                $destination = $uploadDir . $fileName;
                
                if (move_uploaded_file($file['tmp_name'], $destination)) {
                    echo "<div class='success'>";
                    echo "<h4>✅ Upload Successful!</h4>";
                    echo "<p>File saved to: " . htmlspecialchars($destination) . "</p>";
                    echo "<p>File size: " . formatBytes(filesize($destination)) . "</p>";
                    echo "</div>";
                    
                    // Clean up test file
                    unlink($destination);
                    echo "<p><small>Test file cleaned up.</small></p>";
                } else {
                    echo "<div class='error'>";
                    echo "<h4>❌ Failed to move uploaded file</h4>";
                    echo "<p>Could not move from temp location to destination.</p>";
                    echo "</div>";
                }
            } else {
                echo "<div class='error'>";
                echo "<h4>❌ Upload Error</h4>";
                echo "<p>" . getUploadErrorMessage($file['error']) . "</p>";
                echo "</div>";
            }
        } else {
            echo "<div class='error'>";
            echo "<h4>❌ No file uploaded</h4>";
            echo "<p>No file was received by the server.</p>";
            echo "</div>";
        }
        
        echo "</div>";
    }
    
    function formatBytes($size, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        return round($size, $precision) . ' ' . $units[$i];
    }
    
    function getUploadErrorMessage($errorCode) {
        switch ($errorCode) {
            case UPLOAD_ERR_OK:
                return 'No error, file uploaded successfully';
            case UPLOAD_ERR_INI_SIZE:
                return 'File too large (exceeds upload_max_filesize)';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File too large (exceeds MAX_FILE_SIZE in form)';
            case UPLOAD_ERR_PARTIAL:
                return 'File only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'Upload stopped by PHP extension';
            default:
                return 'Unknown upload error';
        }
    }
    ?>
    
    <form method="POST" enctype="multipart/form-data">
        <div class="upload-area">
            <h3>Test File Upload</h3>
            <p>Select a video file to test upload (recommended: under 50MB for testing)</p>
            <input type="file" name="test_file" accept="video/*" required>
            <br><br>
            <button type="submit">Upload Test File</button>
        </div>
    </form>
    
    <div class="info">
        <h3>Instructions:</h3>
        <ol>
            <li>Try uploading a small video file (under 10MB) first</li>
            <li>If that works, try progressively larger files</li>
            <li>Check the error messages to identify the exact issue</li>
            <li>This test bypasses Laravel to isolate server-level problems</li>
        </ol>
    </div>
    
    <p><small>Test page generated at: <?= date('Y-m-d H:i:s') ?></small></p>
</body>
</html>
