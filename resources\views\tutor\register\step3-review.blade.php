@extends('layouts.app')

@section('title', 'Daftar Sebagai Tutor - Review & Submit')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Progress Steps -->
        <div class="mb-8">
            <div class="flex items-center justify-center">
                <div class="flex items-center space-x-4">
                    <!-- Step 1 - Completed -->
                    <div class="flex items-center">
                        <div class="flex items-center justify-center w-10 h-10 bg-green-500 text-white rounded-full">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <span class="ml-2 text-sm font-medium text-green-600">Syarat & Ketentuan</span>
                    </div>

                    <!-- Connector -->
                    <div class="w-16 h-1 bg-green-500"></div>

                    <!-- Step 2 - Completed -->
                    <div class="flex items-center">
                        <div class="flex items-center justify-center w-10 h-10 bg-green-500 text-white rounded-full">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <span class="ml-2 text-sm font-medium text-green-600">Buat Profil</span>
                    </div>

                    <!-- Connector -->
                    <div class="w-16 h-1 bg-green-500"></div>

                    <!-- Step 3 - Active -->
                    <div class="flex items-center">
                        <div class="flex items-center justify-center w-10 h-10 bg-primary text-white rounded-full font-semibold">
                            3
                        </div>
                        <span class="ml-2 text-sm font-medium text-primary">Review & Submit</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="bg-white rounded-lg shadow-sm border p-8">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Review Profil Anda</h1>
                <p class="text-gray-600">Pastikan semua informasi sudah benar sebelum mengirim aplikasi</p>
            </div>

            <!-- Profile Review -->
            <div class="space-y-8">
                <!-- Personal Information -->
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 border-b pb-2">Identitas Diri</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Nama Lengkap</label>
                            <p class="text-gray-900 font-medium">{{ $profile->full_name }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500">Nama Publik</label>
                            <p class="text-gray-900 font-medium">{{ $profile->public_name }}</p>
                            @if($profile->public_name_slug)
                                <p class="text-xs text-gray-500">URL: /tutor/{{ $profile->public_name_slug }}</p>
                            @endif
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500">Nomor Identitas</label>
                            <p class="text-gray-900 font-medium">{{ $profile->identity_number }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500">Nama Bank</label>
                            <p class="text-gray-900 font-medium">{{ $profile->bank_name }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500">Nomor Rekening</label>
                            <p class="text-gray-900 font-medium">{{ $profile->bank_account_number }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500">Pendidikan Terakhir</label>
                            <p class="text-gray-900 font-medium">{{ $profile->education_level }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500">Nomor Telepon</label>
                            <p class="text-gray-900 font-medium">{{ $profile->phone_number }}</p>
                        </div>
                    </div>
                </div>

                <!-- Documents -->
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 border-b pb-2">Dokumen</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Foto Identitas</label>
                            @if($profile->identity_photo_path)
                                <div class="flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-green-600 font-medium">Sudah diunggah</span>
                                </div>
                            @else
                                <span class="text-red-500">Belum diunggah</span>
                            @endif
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500">Portfolio/Resume</label>
                            @if($profile->portfolio_path)
                                <div class="flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-green-600 font-medium">Sudah diunggah</span>
                                </div>
                            @else
                                <span class="text-gray-500">Tidak diunggah</span>
                            @endif
                        </div>

                        @if($profile->npwp_number)
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Nomor NPWP</label>
                            <p class="text-gray-900 font-medium">{{ $profile->npwp_number }}</p>
                        </div>
                        @endif

                        @if($profile->npwp_photo_path)
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Foto NPWP</label>
                            <div class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-green-600 font-medium">Sudah diunggah</span>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Description -->
                @if($profile->description)
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 border-b pb-2">Deskripsi</h2>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-700">{{ $profile->description }}</p>
                    </div>
                </div>
                @endif

                <!-- Agreement Status -->
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 border-b pb-2">Persetujuan</h2>
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-green-600">Syarat dan Ketentuan telah disetujui</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-green-600">Kebijakan Privasi telah disetujui</span>
                        </div>
                    </div>
                </div>

                <!-- Important Notice -->
                {{-- <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <div class="flex items-start space-x-3">
                        <svg class="w-6 h-6 text-blue-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="text-lg font-semibold text-blue-900 mb-2">Informasi Penting</h3>
                            <ul class="text-blue-800 space-y-1 text-sm">
                                <li>• Aplikasi Anda akan ditinjau oleh tim kami dalam 1-3 hari kerja</li>
                                <li>• Anda akan menerima notifikasi melalui email tentang status aplikasi</li>
                                <li>• Pastikan semua informasi yang diberikan akurat dan valid</li>
                                <li>• Setelah disetujui, Anda dapat mulai membuat dan menjual kursus</li>
                            </ul>
                        </div>
                    </div>
                </div> --}}

                <!-- Action Buttons -->
                <div class="flex justify-between pt-6">
                    <a href="{{ route('tutor.register.profile') }}"
                       class="btn btn-outline px-6 py-3">
                        Edit Profil
                    </a>

                    <form action="{{ route('tutor.register.submit') }}" method="POST" class="inline">
                        @csrf
                        <button type="submit"
                                class="btn btn-primary px-8 py-3"
                                onclick="return confirm('Apakah Anda yakin ingin mengirim aplikasi? Data tidak dapat diubah setelah dikirim.')">
                            Kirim Aplikasi
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
