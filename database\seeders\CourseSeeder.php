<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\Category;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get categories and tutors
        $programmingCategory = Category::where('slug', 'programming')->first();
        $dataCategory = Category::where('slug', 'data-science')->first();
        $designCategory = Category::where('slug', 'uiux-design')->first();
        $businessCategory = Category::where('slug', 'business')->first();
        $marketingCategory = Category::where('slug', 'digital-marketing')->first();

        // Get tutors
        $superAdmin = User::where('email', '<EMAIL>')->first();
        $sariDewi = User::where('email', '<EMAIL>')->first();
        $ahmadRahman = User::where('email', '<EMAIL>')->first();

        $courses = [
            // Free Programming Courses
            [
                'tutor_id' => $superAdmin->id,
                'category_id' => $programmingCategory->id,
                'title' => 'Introduction to Programming',
                'description' => 'Learn the basics of programming with hands-on exercises and real-world examples',
                'long_description' => 'This comprehensive course introduces you to the fundamentals of programming. You\'ll learn basic concepts like variables, functions, loops, and conditionals using practical examples. Perfect for absolute beginners who want to start their programming journey.',
                'level' => 'beginner',
                'duration' => '4 jam',
                'price' => 0.00,
                'is_free' => true,
                'language' => 'id',
                'status' => 'published',
                'is_featured' => true,
                'published_at' => now(),
                'learning_outcomes' => [
                    'Memahami konsep dasar pemrograman',
                    'Menulis kode program sederhana',
                    'Menggunakan variabel dan fungsi',
                    'Memahami struktur kontrol program'
                ],
                'requirements' => [
                    'Tidak ada pengalaman programming sebelumnya',
                    'Komputer dengan akses internet',
                    'Motivasi untuk belajar'
                ],
                'target_audience' => [
                    'Pemula yang ingin belajar programming',
                    'Mahasiswa yang ingin memahami dasar-dasar coding',
                    'Profesional yang ingin beralih ke tech'
                ],
                'tags' => ['programming', 'beginner', 'fundamentals', 'free'],
                'total_students' => 2150,
                'average_rating' => 4.6,
                'total_reviews' => 324,
                'total_lessons' => 12,
                'total_duration_minutes' => 240,
            ],
            [
                'tutor_id' => $ahmadRahman->id,
                'category_id' => $programmingCategory->id,
                'title' => 'HTML & CSS Fundamentals',
                'description' => 'Master the building blocks of web development with HTML and CSS',
                'long_description' => 'Learn to create beautiful, responsive websites from scratch. This course covers HTML structure, CSS styling, responsive design principles, and modern web development best practices.',
                'level' => 'beginner',
                'duration' => '6 jam',
                'price' => 0.00,
                'is_free' => true,
                'language' => 'id',
                'status' => 'published',
                'is_featured' => false,
                'published_at' => now(),
                'learning_outcomes' => [
                    'Membuat struktur website dengan HTML',
                    'Mendesain tampilan dengan CSS',
                    'Membuat website responsive',
                    'Memahami best practices web development'
                ],
                'requirements' => [
                    'Pengetahuan dasar komputer',
                    'Text editor (VS Code recommended)',
                    'Web browser modern'
                ],
                'target_audience' => [
                    'Pemula dalam web development',
                    'Designer yang ingin belajar coding',
                    'Siapa saja yang ingin membuat website'
                ],
                'tags' => ['html', 'css', 'web-development', 'responsive', 'free'],
                'total_students' => 1890,
                'average_rating' => 4.7,
                'total_reviews' => 267,
                'total_lessons' => 18,
                'total_duration_minutes' => 360,
            ],

            // Free Data Science Course
            [
                'tutor_id' => $sariDewi->id,
                'category_id' => $dataCategory->id,
                'title' => 'Introduction to Data Science',
                'description' => 'Discover the exciting world of data science and analytics',
                'long_description' => 'Get started with data science fundamentals. Learn about data collection, cleaning, analysis, and visualization. This course provides a solid foundation for anyone interested in pursuing a career in data science.',
                'level' => 'beginner',
                'duration' => '5 jam',
                'price' => 0.00,
                'is_free' => true,
                'language' => 'id',
                'status' => 'published',
                'is_featured' => true,
                'published_at' => now(),
                'learning_outcomes' => [
                    'Memahami konsep dasar data science',
                    'Melakukan analisis data sederhana',
                    'Membuat visualisasi data',
                    'Memahami workflow data science'
                ],
                'requirements' => [
                    'Pengetahuan matematika dasar',
                    'Kemampuan menggunakan komputer',
                    'Minat terhadap data dan analisis'
                ],
                'target_audience' => [
                    'Pemula dalam data science',
                    'Mahasiswa yang ingin belajar analisis data',
                    'Profesional yang ingin memahami data'
                ],
                'tags' => ['data-science', 'analytics', 'visualization', 'beginner', 'free'],
                'total_students' => 1654,
                'average_rating' => 4.5,
                'total_reviews' => 198,
                'total_lessons' => 15,
                'total_duration_minutes' => 300,
            ],

            // Paid Programming Courses
            [
                'tutor_id' => $ahmadRahman->id,
                'category_id' => $programmingCategory->id,
                'title' => 'Frontend Development dengan React',
                'description' => 'Master modern frontend development with React.js and build dynamic web applications',
                'long_description' => 'Comprehensive React.js course covering components, state management, hooks, routing, and modern development practices. Build real-world projects and learn industry best practices.',
                'level' => 'intermediate',
                'duration' => '40 jam',
                'price' => 299000.00,
                'is_free' => false,
                'language' => 'id',
                'status' => 'published',
                'is_featured' => true,
                'published_at' => now(),
                'learning_outcomes' => [
                    'Menguasai React.js dan ecosystem-nya',
                    'Membangun aplikasi web modern',
                    'Menggunakan state management',
                    'Implementasi routing dan navigation',
                    'Deploy aplikasi ke production'
                ],
                'requirements' => [
                    'Pengetahuan HTML, CSS, JavaScript',
                    'Pengalaman basic programming',
                    'Node.js terinstall di komputer'
                ],
                'target_audience' => [
                    'Web developer yang ingin belajar React',
                    'Frontend developer pemula-menengah',
                    'Fullstack developer'
                ],
                'tags' => ['react', 'frontend', 'javascript', 'web-development', 'intermediate'],
                'total_students' => 1250,
                'average_rating' => 4.8,
                'total_reviews' => 189,
                'total_lessons' => 45,
                'total_duration_minutes' => 2400,
            ],
            [
                'tutor_id' => $ahmadRahman->id,
                'category_id' => $programmingCategory->id,
                'title' => 'Backend Development dengan Laravel',
                'description' => 'Build robust backend applications using Laravel PHP framework',
                'long_description' => 'Learn to create powerful backend APIs and web applications with Laravel. Cover database design, authentication, API development, testing, and deployment.',
                'level' => 'intermediate',
                'duration' => '35 jam',
                'price' => 349000.00,
                'is_free' => false,
                'language' => 'id',
                'status' => 'published',
                'is_featured' => true,
                'published_at' => now(),
                'learning_outcomes' => [
                    'Menguasai Laravel framework',
                    'Membangun REST API',
                    'Implementasi authentication & authorization',
                    'Database design dan migration',
                    'Testing dan deployment'
                ],
                'requirements' => [
                    'Pengetahuan PHP dasar',
                    'Pemahaman database SQL',
                    'Pengalaman web development'
                ],
                'target_audience' => [
                    'PHP developer',
                    'Backend developer pemula-menengah',
                    'Fullstack developer'
                ],
                'tags' => ['laravel', 'php', 'backend', 'api', 'intermediate'],
                'total_students' => 980,
                'average_rating' => 4.9,
                'total_reviews' => 156,
                'total_lessons' => 38,
                'total_duration_minutes' => 2100,
            ],

            // Paid Data Science Courses
            [
                'tutor_id' => $sariDewi->id,
                'category_id' => $dataCategory->id,
                'title' => 'Python for Data Science',
                'description' => 'Master Python programming for data analysis and machine learning',
                'long_description' => 'Comprehensive Python course focused on data science applications. Learn pandas, numpy, matplotlib, seaborn, and scikit-learn through hands-on projects.',
                'level' => 'intermediate',
                'duration' => '30 jam',
                'price' => 399000.00,
                'is_free' => false,
                'language' => 'id',
                'status' => 'published',
                'is_featured' => true,
                'published_at' => now(),
                'learning_outcomes' => [
                    'Menguasai Python untuk data science',
                    'Analisis data dengan pandas',
                    'Visualisasi data dengan matplotlib/seaborn',
                    'Machine learning dengan scikit-learn',
                    'Proyek data science end-to-end'
                ],
                'requirements' => [
                    'Pengetahuan programming dasar',
                    'Matematika dan statistik dasar',
                    'Python terinstall di komputer'
                ],
                'target_audience' => [
                    'Data analyst pemula-menengah',
                    'Python developer yang ingin masuk data science',
                    'Mahasiswa STEM'
                ],
                'tags' => ['python', 'data-science', 'pandas', 'machine-learning', 'intermediate'],
                'total_students' => 856,
                'average_rating' => 4.7,
                'total_reviews' => 134,
                'total_lessons' => 42,
                'total_duration_minutes' => 1800,
            ],

            // Additional Free Courses
            [
                'tutor_id' => $superAdmin->id,
                'category_id' => $designCategory->id,
                'title' => 'UI/UX Design Fundamentals',
                'description' => 'Learn the basics of user interface and user experience design',
                'long_description' => 'This course introduces you to the world of UI/UX design. Learn design principles, user research, wireframing, and prototyping. Perfect for beginners who want to start their design journey.',
                'level' => 'beginner',
                'duration' => '3 jam',
                'price' => 0.00,
                'is_free' => true,
                'language' => 'id',
                'status' => 'published',
                'is_featured' => false,
                'published_at' => now(),
                'learning_outcomes' => [
                    'Memahami prinsip-prinsip design',
                    'Melakukan user research',
                    'Membuat wireframe dan prototype',
                    'Menggunakan tools design'
                ],
                'requirements' => [
                    'Tidak ada pengalaman design sebelumnya',
                    'Komputer dengan akses internet',
                    'Minat terhadap design'
                ],
                'target_audience' => [
                    'Pemula dalam UI/UX design',
                    'Developer yang ingin belajar design',
                    'Siapa saja yang tertarik dengan design'
                ],
                'tags' => ['ui-ux', 'design', 'beginner', 'free'],
                'total_students' => 1234,
                'average_rating' => 4.4,
                'total_reviews' => 156,
                'total_lessons' => 10,
                'total_duration_minutes' => 180,
            ],

            // Additional Paid Courses
            [
                'tutor_id' => $sariDewi->id,
                'category_id' => $businessCategory->id,
                'title' => 'Digital Marketing Strategy',
                'description' => 'Master digital marketing strategies for modern businesses',
                'long_description' => 'Comprehensive digital marketing course covering SEO, social media marketing, content marketing, email marketing, and analytics. Learn to create effective marketing campaigns.',
                'level' => 'intermediate',
                'duration' => '25 jam',
                'price' => 249000.00,
                'is_free' => false,
                'language' => 'id',
                'status' => 'published',
                'is_featured' => false,
                'published_at' => now(),
                'learning_outcomes' => [
                    'Membuat strategi digital marketing',
                    'Menguasai SEO dan SEM',
                    'Social media marketing',
                    'Content marketing strategy',
                    'Analytics dan reporting'
                ],
                'requirements' => [
                    'Pengetahuan dasar marketing',
                    'Pengalaman menggunakan internet',
                    'Akses ke tools marketing'
                ],
                'target_audience' => [
                    'Marketing professional',
                    'Business owner',
                    'Entrepreneur'
                ],
                'tags' => ['digital-marketing', 'seo', 'social-media', 'business'],
                'total_students' => 678,
                'average_rating' => 4.6,
                'total_reviews' => 89,
                'total_lessons' => 32,
                'total_duration_minutes' => 1500,
            ],
        ];

        foreach ($courses as $courseData) {
            Course::create($courseData);
        }
    }
}
