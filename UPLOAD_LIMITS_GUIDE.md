# 📁 Upload Limits Configuration Guide

## 🚨 Current Status
Your server currently has a **150MB upload limit** configured in PHP. The application is now set to **100MB** for video uploads.

## 🎯 Quick Fix (Updated)
✅ **DONE:** Updated upload limits to 100MB in:
- Controller validation
- Frontend file size checks
- User interface messages

## 🔧 Permanent Solution: Increase XAMPP Upload Limits

### Step 1: Edit PHP Configuration
1. **Open XAMPP Control Panel**
2. **Stop Apache** if it's running
3. **Navigate to:** `C:\xampp\php\php.ini`
4. **Open php.ini** in a text editor (Notepad++ recommended)

### Step 2: Find and Update These Settings
Search for these lines and change their values:

```ini
; File upload settings
file_uploads = On
upload_max_filesize = 150M
post_max_size = 150M
max_file_uploads = 20

; Execution time settings
max_execution_time = 600
max_input_time = 600
memory_limit = 512M

; Other useful settings
max_input_vars = 3000
```

### Step 3: Restart XAMPP
1. **Start Apache** again in XAMPP Control Panel
2. **Verify changes** by visiting: `http://127.0.0.1:8000/server-info.php`

### Step 4: Update Application Limits (After XAMPP Config)
Once XAMPP is configured, update these files back to 150MB:

#### File: `app/Http/Controllers/Tutor/CurriculumController.php`
```php
// Line 159: Change max validation
'video_file' => 'required|file|mimes:mp4,mov,avi,quicktime|max:102400', // 100MB max

// Line 170: Change size check
$maxSize = 100 * 1024 * 1024; // 100MB in bytes
'message' => 'File terlalu besar. Maksimal 100MB.'
```

#### File: `resources/views/tutor/curriculum/index.blade.php`
```javascript
// Line 429: Update UI message
<p class="text-xs text-gray-500">MP4, MOV, AVI (Max: 100MB)</p>

// Line 601: Update JavaScript check
const maxSize = 100 * 1024 * 1024; // 100MB in bytes
alert('File terlalu besar! Maksimal 100MB. File Anda: ' + formatFileSize(file.size));
```

## 🧪 Testing Steps

### 1. Test Current 100MB Limit
- Try uploading a video file under 100MB
- Should work without 413 errors

### 2. Test with Larger Files
- Upload a file between 100-150MB (should be rejected by app)
- Upload a file over 150MB (should be rejected by server)

### 3. Verification URLs
- **Server Info:** `http://127.0.0.1:8000/server-info.php`
- **Direct Upload Test:** `http://127.0.0.1:8000/test-upload.php`

## 🎯 Alternative Solutions

### Option 1: Use Different Server
If XAMPP configuration doesn't work:
```bash
# Install and use Laravel Valet (Windows)
# Or use Docker with custom PHP configuration
```

### Option 2: Chunked Upload (Advanced)
Implement chunked upload for very large files:
- Split large files into smaller chunks
- Upload chunks sequentially
- Reassemble on server

### Option 3: Cloud Storage
Use cloud storage services:
- AWS S3 with direct upload
- Google Cloud Storage
- Cloudinary for video processing

## 📋 Troubleshooting

### If 413 Error Persists:
1. **Check Apache configuration** (`httpd.conf`)
2. **Verify .htaccess** is being read
3. **Check Windows file permissions**
4. **Disable antivirus temporarily** during testing

### If Upload is Slow:
1. **Increase timeout values** in php.ini
2. **Check network connection**
3. **Use SSD storage** for better performance

## 🎉 Success Indicators

✅ Server info shows 150MB limits
✅ No 413 errors during upload
✅ Progress bar works smoothly
✅ Files are stored correctly
✅ Video playback works

## 📞 Need Help?

If you encounter issues:
1. Check the Laravel logs: `storage/logs/laravel.log`
2. Check PHP error logs in XAMPP
3. Test with the diagnostic tools provided
4. Try uploading progressively larger files to find the exact limit

---

**Current Status:** ✅ Working with 100MB limit
**Server Limit:** ✅ 150MB configured in PHP
**App Limit:** ✅ 100MB for optimal performance
