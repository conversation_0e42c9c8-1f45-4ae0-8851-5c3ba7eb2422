<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class TutorController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth', 'is.tutor']);
    }

    /**
     * Show the tutor dashboard.
     */
    public function dashboard()
    {
        $user = Auth::user();

        // Mock data for tutor dashboard stats
        $stats = [
            'total_courses' => 0,
            'active_students' => 0,
            'total_revenue' => 0,
            'course_ratings' => 0,
            'pending_reviews' => 0,
            'monthly_earnings' => 0
        ];

        // Mock data for recent activities
        $recentActivities = [];

        // Mock data for course performance
        $coursePerformance = [];

        return view('tutor.dashboard', compact('user', 'stats', 'recentActivities', 'coursePerformance'));
    }

    /**
     * Show the tutor courses page.
     */
    public function courses()
    {
        $user = Auth::user();

        // Get real courses from database
        $publishedCourses = \App\Models\Course::with(['category', 'chapters', 'lessons'])
            ->where('tutor_id', $user->id)
            ->where('status', 'published')
            ->orderBy('created_at', 'desc')
            ->get();

        $draftCourses = \App\Models\Course::with(['category', 'chapters', 'lessons'])
            ->where('tutor_id', $user->id)
            ->where('status', 'draft')
            ->orderBy('updated_at', 'desc')
            ->get();

        return view('tutor.courses', compact('user', 'publishedCourses', 'draftCourses'));
    }

    /**
     * Show the create course page.
     */
    public function createCourse()
    {
        $user = Auth::user();
        $categories = \App\Models\Category::active()->get();
        return view('tutor.create-course', compact('user', 'categories'));
    }

    /**
     * Store a new course.
     */
    public function storeCourse(Request $request)
    {
        $user = Auth::user();

        // Debug: Log incoming request data
        Log::info('Course creation attempt', [
            'user_id' => $user->id,
            'request_data' => $request->all(),
            'has_file' => $request->hasFile('thumbnail')
        ]);

        // Validate the request
        $validated = $request->validate([
            'course_title' => 'required|string|max:255',
            'course_category' => 'required|exists:categories,id',
            'course_description' => 'required|string|min:50',
            'course_level' => 'required|in:beginner,intermediate,advanced',
            'course_duration' => 'nullable|string|max:100',
            'course_type' => 'required|in:free,paid',
            'course_price' => 'required_if:course_type,paid|numeric|min:0',
            'learning_outcomes' => 'nullable|array',
            'learning_outcomes.*' => 'nullable|string|max:255',
            'requirements' => 'nullable|array',
            'requirements.*' => 'nullable|string|max:255',
            'target_audience' => 'nullable|array',
            'target_audience.*' => 'nullable|string|max:255',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:10240', // 10MB max
        ]);

        // Filter out empty values from arrays
        $learningOutcomes = array_filter($request->input('learning_outcomes', []), function($value) {
            return !empty(trim($value));
        });

        $requirements = array_filter($request->input('requirements', []), function($value) {
            return !empty(trim($value));
        });

        $targetAudience = array_filter($request->input('target_audience', []), function($value) {
            return !empty(trim($value));
        });

        try {
            Log::info('Creating course with data', [
                'learning_outcomes' => $learningOutcomes,
                'requirements' => $requirements,
                'target_audience' => $targetAudience
            ]);

            // Determine if course is free and set price
            $isFree = $validated['course_type'] === 'free';
            $price = $isFree ? 0 : ($validated['course_price'] ?? 0);

            // Create the course first to get the course ID
            $course = \App\Models\Course::create([
                'tutor_id' => $user->id,
                'category_id' => $validated['course_category'],
                'title' => $validated['course_title'],
                'description' => $validated['course_description'],
                'level' => $validated['course_level'],
                'duration' => $validated['course_duration'],
                'price' => $price,
                'is_free' => $isFree,
                'learning_outcomes' => $learningOutcomes,
                'requirements' => $requirements,
                'target_audience' => $targetAudience,
                'status' => 'draft',
            ]);

            Log::info('Course created successfully', ['course_id' => $course->id]);

            // Handle thumbnail upload with custom path structure including thumbnail folder
            if ($request->hasFile('thumbnail')) {
                Log::info('Processing thumbnail upload');
                $thumbnailPath = $request->file('thumbnail')->store(
                    "user/{$user->id}/course/{$course->id}/thumbnail",
                    'public'
                );

                Log::info('Thumbnail uploaded', ['path' => $thumbnailPath]);

                // Update course with thumbnail path
                $course->update(['thumbnail' => $thumbnailPath]);

                Log::info('Course updated with thumbnail path');
            }

            Log::info('Redirecting to courses page');
            return redirect()->route('tutor.courses')->with('success', 'Kursus berhasil dibuat! Anda dapat melanjutkan dengan menambahkan kurikulum.');

        } catch (\Exception $e) {
            Log::error('Course creation error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return back()->withInput()->with('error', 'Terjadi kesalahan saat membuat kursus. Silakan coba lagi.');
        }
    }

    /**
     * Show the tutor students page.
     */
    public function students()
    {
        $user = Auth::user();

        // Mock data for enrolled students
        $enrolledStudents = [];

        return view('tutor.students', compact('user', 'enrolledStudents'));
    }

    /**
     * Show the tutor analytics page.
     */
    public function analytics()
    {
        $user = Auth::user();

        // Mock data for analytics
        $analyticsData = [
            'course_views' => [],
            'enrollment_trends' => [],
            'revenue_analytics' => [],
            'student_engagement' => []
        ];

        return view('tutor.analytics', compact('user', 'analyticsData'));
    }

    /**
     * Show the tutor earnings page.
     */
    public function earnings()
    {
        $user = Auth::user();

        // Mock data for earnings
        $earningsData = [
            'total_earnings' => 0,
            'monthly_earnings' => 0,
            'pending_payouts' => 0,
            'transaction_history' => []
        ];

        return view('tutor.earnings', compact('user', 'earningsData'));
    }

    /**
     * Show the tutor profile page.
     */
    public function profile()
    {
        $user = Auth::user();
        return view('tutor.profile', compact('user'));
    }

    /**
     * Show the tutor settings page.
     */
    public function settings()
    {
        $user = Auth::user();
        return view('tutor.settings', compact('user'));
    }

    /**
     * Update tutor profile.
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        // Handle delete profile picture request
        if ($request->has('delete_profile_picture')) {
            if ($user->profile_picture) {
                \App\Services\FileStorageService::deletePublicFile($user->profile_picture);
                $user->update(['profile_picture' => null]);
            }
            return back()->with('profile_success', 'Foto profil berhasil dihapus!');
        }

        // Handle profile picture upload only
        if ($request->hasFile('profile_picture')) {
            $request->validate([
                'profile_picture' => 'required|image|mimes:jpeg,png,jpg|max:2048',
            ], [
                'profile_picture.image' => 'File harus berupa gambar.',
                'profile_picture.max' => 'Ukuran file maksimal 2MB.',
                'profile_picture.mimes' => 'Format file harus JPG, PNG, atau JPEG.',
            ]);

            // Delete old profile picture if exists
            if ($user->profile_picture) {
                \App\Services\FileStorageService::deletePublicFile($user->profile_picture);
            }

            $profilePicturePath = \App\Services\FileStorageService::storePublicUserFile(
                $request->file('profile_picture'),
                $user->id,
                'profile'
            );

            $user->update(['profile_picture' => $profilePicturePath]);
            return back()->with('profile_success', 'Foto profil berhasil diperbarui!');
        }

        // Handle other profile data
        $request->validate([
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'long_description' => 'nullable|string|max:10000',
        ]);

        $user->update([
            'name' => $request->display_name,
        ]);

        // Update tutor profile if exists
        if ($user->tutorProfile) {
            $user->tutorProfile->update([
                'description' => $request->description,
                'long_description' => $request->long_description,
            ]);
        }

        return back()->with('success', 'Profil tutor berhasil diperbarui!');
    }
}
