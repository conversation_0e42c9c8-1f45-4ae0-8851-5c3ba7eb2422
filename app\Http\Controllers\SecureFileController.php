<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\CourseLesson;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class SecureFileController extends Controller
{
    /**
     * Serve protected video files for course lessons.
     */
    public function serveVideo(Course $course, CourseLesson $lesson)
    {
        // Check if lesson belongs to the course
        if ($lesson->course_id !== $course->id) {
            abort(404);
        }

        // Check access permissions
        if (!$this->canAccessLesson($course, $lesson)) {
            abort(403, 'Anda tidak memiliki akses ke materi ini.');
        }

        // Check if lesson has a video file
        if (!$lesson->video_file) {
            abort(404, 'File video tidak ditemukan.');
        }

        // Get file path
        $filePath = $lesson->video_file;

        // Check if file exists in storage
        if (!Storage::disk('local')->exists($filePath)) {
            abort(404, 'File video tidak ditemukan.');
        }

        // Get file content and info
        $fileContent = Storage::disk('local')->get($filePath);
        $fullPath = storage_path('app/' . $filePath);
        $mimeType = mime_content_type($fullPath);
        $fileName = basename($filePath);

        // Return file response with proper headers
        return response($fileContent)
            ->header('Content-Type', $mimeType)
            ->header('Content-Disposition', 'inline; filename="' . $fileName . '"')
            ->header('Cache-Control', 'private, max-age=3600')
            ->header('X-Accel-Buffering', 'no'); // For nginx streaming
    }

    /**
     * Serve protected material files for course lessons.
     */
    public function serveMaterial(Course $course, CourseLesson $lesson, $filename)
    {
        // Check if lesson belongs to the course
        if ($lesson->course_id !== $course->id) {
            abort(404);
        }

        // Check access permissions
        if (!$this->canAccessLesson($course, $lesson)) {
            abort(403, 'Anda tidak memiliki akses ke materi ini.');
        }

        // Construct file path
        $filePath = "user/{$course->tutor_id}/course/{$course->id}/materials/{$filename}";

        // Check if file exists in storage
        if (!Storage::disk('local')->exists($filePath)) {
            abort(404, 'File materi tidak ditemukan.');
        }

        // Get file content and info
        $fileContent = Storage::disk('local')->get($filePath);
        $fullPath = storage_path('app/' . $filePath);
        $mimeType = mime_content_type($fullPath);

        // Return file response with proper headers
        return response($fileContent)
            ->header('Content-Type', $mimeType)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->header('Cache-Control', 'private, max-age=3600');
    }

    /**
     * Check if user can access a specific lesson.
     */
    private function canAccessLesson(Course $course, CourseLesson $lesson): bool
    {
        // If course is free, everyone can access
        if ($course->is_free) {
            return true;
        }

        // If lesson is preview, everyone can access
        if ($lesson->is_preview) {
            return true;
        }

        // If user is not authenticated, deny access
        if (!Auth::check()) {
            return false;
        }

        $user = Auth::user();

        // If user is the course tutor, allow access
        if ($user->id === $course->tutor_id) {
            return true;
        }

        // Check if user has purchased the course
        return $this->hasUserPurchasedCourse($user, $course);
    }

    /**
     * Check if user has purchased the course.
     */
    private function hasUserPurchasedCourse($user, Course $course): bool
    {
        return $user->enrollments()
                   ->where('course_id', $course->id)
                   ->where('status', 'active')
                   ->exists();
    }
}
