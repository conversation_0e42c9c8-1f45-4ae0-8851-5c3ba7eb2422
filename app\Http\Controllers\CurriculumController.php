<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Category;
use Illuminate\Http\Request;

class CurriculumController extends Controller
{
    /**
     * Display the curriculum page with all available courses.
     */
    public function index(Request $request)
    {
        // Get all active categories for filter
        $categories = Category::active()->orderBy('sort_order')->get();

        // Start building the query
        $query = Course::with(['tutor', 'category'])
            ->published()
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        if ($request->filled('price_type')) {
            if ($request->price_type === 'free') {
                $query->where('price', 0);
            } elseif ($request->price_type === 'paid') {
                $query->where('price', '>', 0);
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereJsonContains('tags', $search);
            });
        }

        // Get courses with pagination
        $courses = $query->paginate(12)->withQueryString();

        // Get course statistics
        $stats = [
            'total_courses' => Course::published()->count(),
            'free_courses' => Course::published()->where('price', 0)->count(),
            'paid_courses' => Course::published()->where('price', '>', 0)->count(),
            'total_students' => Course::published()->sum('total_students'),
        ];

        return view('curriculum', compact('courses', 'categories', 'stats'));
    }

    /**
     * Show a specific course detail.
     */
    public function show(Course $course)
    {
        // Make sure the course is published
        if ($course->status !== 'published') {
            abort(404);
        }

        // Load relationships
        $course->load(['tutor', 'category']);

        // Get related courses (same category, different course)
        $relatedCourses = Course::with(['tutor', 'category'])
            ->published()
            ->where('category_id', $course->category_id)
            ->where('id', '!=', $course->id)
            ->limit(3)
            ->get();

        return view('course-detail', compact('course', 'relatedCourses'));
    }
}
