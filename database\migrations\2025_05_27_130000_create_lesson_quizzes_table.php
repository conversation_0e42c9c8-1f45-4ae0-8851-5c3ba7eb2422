<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lesson_quizzes', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('lesson_id'); // Foreign key to course_lessons table
            
            // Quiz Information
            $table->string('title'); // Quiz title
            $table->text('description')->nullable(); // Quiz description/instructions
            $table->integer('time_limit')->nullable(); // Time limit in minutes (null = no limit)
            $table->integer('max_attempts')->default(1); // Maximum attempts allowed
            $table->integer('passing_score')->default(70); // Minimum score to pass (percentage)
            $table->boolean('shuffle_questions')->default(false); // Randomize question order
            $table->boolean('show_results_immediately')->default(true); // Show results after submission
            $table->boolean('is_published')->default(false);
            
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('lesson_id')->references('id')->on('course_lessons')->onDelete('cascade');
            
            // Indexes
            $table->index('lesson_id');
            $table->index('is_published');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lesson_quizzes');
    }
};
