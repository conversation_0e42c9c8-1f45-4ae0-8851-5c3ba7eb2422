<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('course_chapters', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('course_id'); // Foreign key to courses table

            // Chapter Information
            $table->string('title'); // Chapter title (e.g., "Bab 1: Pengenalan Programming")
            $table->string('slug'); // URL-friendly slug
            $table->text('description')->nullable(); // Chapter description
            $table->integer('sort_order')->default(0); // Order of chapters
            $table->integer('duration_minutes')->default(0); // Total duration of chapter

            // Status
            $table->boolean('is_published')->default(false);
            $table->boolean('is_free')->default(false); // Whether this chapter is free (for paid courses)

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('cascade');

            // Indexes
            $table->index('course_id');
            $table->index('sort_order');
            $table->index('is_published');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('course_chapters');
    }
};
