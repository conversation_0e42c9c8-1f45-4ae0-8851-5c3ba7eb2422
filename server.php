<?php

/**
 * Custom Laravel development server with increased upload limits
 * Use this instead of 'php artisan serve'
 */

// Set upload limits before starting the server
ini_set('upload_max_filesize', '150M');
ini_set('post_max_size', '150M');
ini_set('max_execution_time', '600');
ini_set('max_input_time', '600');
ini_set('memory_limit', '512M');
ini_set('file_uploads', '1');
ini_set('max_file_uploads', '20');

// Laravel's default server.php content
$uri = urldecode(
    parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH) ?? ''
);

// This file allows us to emulate Apache's "mod_rewrite" functionality from the
// built-in PHP web server. This provides a convenient way to test a Laravel
// application without having installed a "real" web server software here.
if ($uri !== '/' && file_exists(__DIR__.'/public'.$uri)) {
    return false;
}

require_once __DIR__.'/public/index.php';
