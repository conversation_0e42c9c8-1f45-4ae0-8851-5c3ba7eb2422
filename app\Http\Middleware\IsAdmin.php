<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class IsAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Anda harus login terlebih dahulu.');
        }

        $user = Auth::user();

        // Check if user is admin or superadmin
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            abort(403, '<PERSON><PERSON><PERSON> ditolak. Anda harus menjadi admin untuk mengakses halaman ini.');
        }

        return $next($request);
    }
}
