# File Storage Structure Documentation

## Overview
This document describes the organized file storage structure implemented in the Ngambiskuy platform.

## Storage Organization

### Public Files (Accessible via URL)
**Location:** `storage/app/public/`

#### User Files
- **Profile Pictures:** `storage/app/public/user/{user_id}/profile/`
- **Cover Images:** `storage/app/public/user/{user_id}/cover/` (future use)

**Example:**
```
storage/app/public/user/
├── 550e8400-e29b-41d4-a716-446655440000/
│   ├── profile/
│   │   └── 2024-01-15_14-30-25_abc12345.jpg
│   └── cover/
│       └── 2024-01-15_14-35-10_def67890.jpg
└── 550e8400-e29b-41d4-a716-446655440001/
    └── profile/
        └── 2024-01-15_15-20-30_ghi09876.png
```

### Private Files (Not Publicly Accessible)
**Location:** `storage/app/private/`

#### Tutor Documents
- **KTP/Identity Documents:** `storage/app/private/tutor/{tutor_id}/ktp/`
- **NPWP Documents:** `storage/app/private/tutor/{tutor_id}/npwp/`
- **Portfolio/Resume:** `storage/app/private/tutor/{tutor_id}/portfolio/`

**Example:**
```
storage/app/private/tutor/
├── 660e8400-e29b-41d4-a716-446655440000/
│   ├── ktp/
│   │   └── 2024-01-15_14-30-25_abc12345.jpg
│   ├── npwp/
│   │   └── 2024-01-15_14-32-15_def67890.jpg
│   └── portfolio/
│       └── 2024-01-15_14-35-45_resume.pdf
└── 660e8400-e29b-41d4-a716-446655440001/
    ├── ktp/
    │   └── 2024-01-15_15-20-30_ghi09876.png
    └── portfolio/
        └── 2024-01-15_15-25-10_portfolio.pdf
```

## File Naming Convention

All uploaded files are renamed using the following pattern:
```
{timestamp}_{random_string}.{extension}
```

**Example:** `2024-01-15_14-30-25_abc12345.jpg`

- **timestamp:** `Y-m-d_H-i-s` format
- **random_string:** 8-character random string
- **extension:** Original file extension

## FileStorageService Methods

### Public File Methods
- `storePublicUserFile($file, $userId, $folder)` - Store public files
- `deletePublicFile($path)` - Delete public files
- `getPublicFileUrl($path)` - Get public file URL

### Private File Methods
- `storePrivateTutorFile($file, $tutorId, $folder)` - Store private files
- `deletePrivateFile($path)` - Delete private files
- `privateFileExists($path)` - Check if private file exists

### Utility Methods
- `ensureDirectoriesExist()` - Create base directories
- `getFileSize($path, $disk)` - Get human-readable file size
- `cleanupOldFiles($daysOld)` - Maintenance method for cleanup

## Security Considerations

1. **Private Files:** Stored in `storage/app/private/` which is not web-accessible
2. **File Validation:** All uploads are validated for type and size
3. **Unique Naming:** Prevents file conflicts and adds security through obscurity
4. **Access Control:** Private files require authentication and authorization

## Usage Examples

### Storing Profile Picture
```php
$profilePicturePath = FileStorageService::storePublicUserFile(
    $request->file('profile_picture'), 
    $user->id, 
    'profile'
);
```

### Storing KTP Document
```php
$ktpPath = FileStorageService::storePrivateTutorFile(
    $request->file('identity_photo'), 
    $tutorProfile->id, 
    'ktp'
);
```

### Getting Profile Picture URL
```php
$url = FileStorageService::getPublicFileUrl($user->profile_picture);
```

## Migration Notes

- Old files in `tutor/identity/`, `tutor/portfolio/`, `tutor/npwp/` will continue to work
- New uploads will use the organized structure
- Consider running a migration script to move existing files to new structure

## Maintenance

- Run `FileStorageService::ensureDirectoriesExist()` during deployment
- Consider implementing periodic cleanup of orphaned files
- Monitor storage usage and implement archiving if needed
