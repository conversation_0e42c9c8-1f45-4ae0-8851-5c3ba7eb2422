<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ngambiskuy - Tech Courses</title>
    <link rel="stylesheet" href="{{ asset('css/style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /* General Styles */
        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
        }

/* Navbar Styles */
header {
    background: #333;
    color: #fff;
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header .logo a {
    color: #fff;
    text-decoration: none;
    font-size: 24px;
    font-weight: 600;
}

header nav ul.nav-links {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
}

header nav ul.nav-links li {
    margin-left: 25px;
}

header nav ul.nav-links li a {
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

header nav ul.nav-links li a:hover {
    color: #007bff; /* Highlight color on hover */
}

/* Hamburger Menu (Mobile) */
header .hamburger {
    display: none;
    font-size: 24px;
    cursor: pointer;
}

/* Responsive Navbar */
@media (max-width: 768px) {
    header nav ul.nav-links {
        display: none;
        flex-direction: column;
        background: #333;
        position: absolute;
        top: 60px;
        left: 0;
        width: 100%;
        padding: 20px 0;
    }

    header nav ul.nav-links.active {
        display: flex;
    }

    header nav ul.nav-links li {
        margin: 10px 0;
    }

    header .hamburger {
        display: block;
    }
}

        /* Hero Section */
        .hero {
            background: #007bff;
            color: #fff;
            padding: 100px 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .hero p {
            font-size: 18px;
            margin-bottom: 40px;
        }

        .cta-buttons .btn {
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
        }

        .cta-buttons .btn-primary {
            background: #fff;
            color: #007bff;
        }

        .cta-buttons .btn-secondary {
            background: transparent;
            border: 2px solid #fff;
            color: #fff;
        }

        /* Courses Section */
        .courses {
            padding: 80px 0;
            text-align: center;
        }

        .courses h2 {
            font-size: 36px;
            margin-bottom: 40px;
        }

        .course-categories {
            display: flex;
            justify-content: space-around;
        }

        .course-card {
            background: #f4f4f4;
            padding: 20px;
            border-radius: 10px;
            width: 30%;
        }

        .course-card h3 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        /* Footer Styles */
        footer {
            background: #333;
            color: #fff;
            padding: 40px 0;
            text-align: center;
        }

        footer .footer-links ul {
            list-style: none;
            padding: 0;
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        footer .footer-links ul li {
            margin: 0 15px;
        }

        footer .footer-links ul li a {
            color: #fff;
            text-decoration: none;
        }

        footer .social-media a {
            color: #fff;
            margin: 0 10px;
            text-decoration: none;
        }

        /* ... (Keep previous styles) ... */

/* Free Courses Section */
.free-courses {
    background: #f8f9fa; /* Light background */
    padding: 80px 0;
}

.free-courses h2 {
    color: #007bff; /* Brand color for free courses */
    font-size: 36px;
    text-align: center;
    margin-bottom: 30px;
}

/* Paid Courses Section */
.paid-courses {
    background: #ffffff;
    padding: 80px 0;
}

.paid-courses h2 {
    color: #28a745; /* Green accent for paid courses */
    font-size: 36px;
    text-align: center;
    margin-bottom: 30px;
}

/* Course List Grid (Applies to both free/paid) */
.course-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

/* Course Item Cards */
.course-item {
    background: #fff;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.course-item:hover {
    transform: translateY(-5px);
}

/* Style Free vs Paid Course Buttons */
.free-courses .btn-primary {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 5px;
}

.paid-courses .btn-secondary {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 5px;
}

/* Price Tag for Paid Courses (Optional) */
.price-tag {
    background: #28a745;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    display: inline-block;
    margin: 10px 0;
    font-weight: 600;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .course-categories {
        flex-direction: column;
        gap: 20px;
    }

    .course-card {
        width: 100%;
    }

    .hero h1 {
        font-size: 36px;
    }
}
    </style>
</head>

<body>
    <header>
        <div class="container">
            <div class="logo">
                <a href="/">Ngambiskuy</a>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="#courses">Courses</a></li>
                    <li><a href="#free-courses">Free Courses</a></li>
                    <li><a href="#paid-courses">Paid Courses</a></li>
                    <li><a href="#about">About Us</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
                <div class="hamburger" id="hamburger">
                    <i class="fas fa-bars"></i>
                </div>
            </nav>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <h1>Unlock Your Tech Potential</h1>
            <p>Learn Data, Web Development, Game Development, and more with Ngambiskuy.</p>
            <div class="cta-buttons">
                <a href="#free-courses" class="btn btn-primary">Start Free Course</a>
                <a href="#paid-courses" class="btn btn-secondary">Explore Paid Courses</a>
            </div>
        </div>
    </section>

    <section id="courses" class="courses">
        <div class="container">
            <h2>Our Main Courses</h2>
            <div class="course-categories">
                <div class="course-card">
                    <h3>Data Science</h3>
                    <p>Master data analysis, machine learning, and more.</p>
                </div>
                <div class="course-card">
                    <h3>Web Development</h3>
                    <p>Build modern websites and web applications.</p>
                </div>
                <div class="course-card">
                    <h3>Game Development</h3>
                    <p>Create your own games from scratch.</p>
                </div>
            </div>
        </div>
    </section>

    <section id="free-courses" class="free-courses">
        <div class="container">
            <h2>Free Courses</h2>
            <p>Start learning for free with our beginner-friendly courses.</p>
            <div class="course-list">
                <!-- Example Free Course -->
                <div class="course-item">
                    <h3>Introduction to Python</h3>
                    <p>Learn the basics of Python programming.</p>
                    <a href="#" class="btn btn-primary">Enroll Now</a>
                </div>
            </div>
        </div>
    </section>

    <section id="paid-courses" class="paid-courses">
        <div class="container">
            <h2>Paid Courses</h2>
            <p>Take your skills to the next level with our premium courses.</p>
            <div class="course-list">
                <!-- Example Paid Course -->
                <div class="course-item">
                    <h3>Advanced Web Development</h3>
                    <p>Learn advanced concepts in web development.</p>
                    <div class="price-tag">$99</div> <!-- Add price -->
                    <a href="#" class="btn btn-secondary">Enroll Now</a>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-links">
                <ul>
                    <li><a href="#about">About Us</a></li>
                    <li><a href="#contact">Contact</a></li>
                    <li><a href="#privacy">Privacy Policy</a></li>
                </ul>
            </div>
            <div class="social-media">
                <a href="#">Facebook</a>
                <a href="#">Twitter</a>
                <a href="#">Instagram</a>
            </div>
            <p>&copy; 2023 Ngambiskuy. All rights reserved.</p>
        </div>
    </footer>

    <script>
        document.getElementById('hamburger').addEventListener('click', function () {
            const navLinks = document.querySelector('.nav-links');
            navLinks.classList.toggle('active');
        });
    </script>
</body>



</html>
