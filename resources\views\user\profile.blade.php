@extends('layouts.user')

@section('title', '<PERSON><PERSON>')

@section('content')
<div class="p-6">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Profil <PERSON></h1>
                <p class="text-gray-600 mt-1">Kelola informasi profil dan preferensi akun <PERSON></p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Profile Picture & Basic Info -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="text-center">
                    <div class="relative inline-block">
                        @if($user->profile_picture)
                            <img src="{{ $user->getProfilePictureUrl() }}" alt="Profile" class="w-24 h-24 rounded-full object-cover mx-auto mb-4 border-4 border-gray-200">
                        @else
                            <div class="w-24 h-24 bg-gradient-to-br from-primary to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        @endif
                    </div>
                    <h3 class="text-lg font-bold text-gray-900">{{ $user->name }}</h3>
                    <p class="text-sm text-gray-600">{{ $user->email }}</p>
                    <div class="mt-3">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Akun Terverifikasi
                        </span>
                    </div>

                    <!-- Profile Picture Upload Form -->
                    <form action="{{ route('user.profile.picture.update') }}" method="POST" enctype="multipart/form-data" class="mt-4">
                        @csrf
                        <div class="space-y-2">
                            <input type="file" id="profile_picture" name="profile_picture" accept="image/jpeg,image/png,image/jpg" class="hidden" onchange="this.form.submit()">
                            <label for="profile_picture" class="btn btn-outline w-full btn-sm cursor-pointer">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Ubah Foto Profil
                            </label>
                            @if($user->profile_picture)
                                <button type="submit" name="delete_profile_picture" value="1" class="btn btn-outline w-full btn-sm text-red-600 hover:bg-red-50">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    Hapus Foto
                                </button>
                            @endif
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mt-6">
                <h4 class="font-semibold text-gray-900 mb-4">Statistik Belajar</h4>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Kursus Diikuti</span>
                        <span class="font-medium text-gray-900">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Sertifikat</span>
                        <span class="font-medium text-gray-900">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Poin XP</span>
                        <span class="font-medium text-gray-900">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Bergabung</span>
                        <span class="font-medium text-gray-900">{{ $user->created_at->format('M Y') }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Form -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Informasi Profil</h3>

                <form action="{{ route('user.profile.update') }}" method="POST" class="space-y-6">
                    @csrf
                    @method('PUT')

                    @if(session('success'))
                        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                            <ul class="list-disc list-inside">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Nama Lengkap</label>
                            <input type="text" id="name" name="name" value="{{ old('name', $user->name) }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('name') border-red-500 @enderror">
                            @error('name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" id="email" name="email" value="{{ $user->email }}" readonly
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500">
                            <p class="text-xs text-gray-500 mt-1">Email tidak dapat diubah</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="job_title" class="block text-sm font-medium text-gray-700 mb-2">Pekerjaan</label>
                            <input type="text" id="job_title" name="job_title" value="{{ old('job_title', $user->job_title) }}" placeholder="Mahasiswa, Developer, Designer, dll."
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('job_title') border-red-500 @enderror">
                            @error('job_title')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="company" class="block text-sm font-medium text-gray-700 mb-2">Perusahaan</label>
                            <input type="text" id="company" name="company" value="{{ old('company', $user->company) }}" placeholder="Nama perusahaan/institusi"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('company') border-red-500 @enderror">
                            @error('company')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div>
                        <label for="bio" class="block text-sm font-medium text-gray-700 mb-2">Bio</label>
                        <textarea id="bio" name="bio" rows="4" placeholder="Ceritakan sedikit tentang diri Anda..."
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('bio') border-red-500 @enderror">{{ old('bio', $user->bio) }}</textarea>
                        @error('bio')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-2">Lokasi</label>
                        <input type="text" id="location" name="location" value="{{ old('location', $user->location) }}" placeholder="Kota, Provinsi"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('location') border-red-500 @enderror">
                        @error('location')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="pendidikan" class="block text-sm font-medium text-gray-700 mb-2">Pendidikan</label>
                            <select id="pendidikan" name="pendidikan"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('pendidikan') border-red-500 @enderror">
                                <option value="">Pilih tingkat pendidikan</option>
                                <option value="sma" {{ old('pendidikan', $user->pendidikan) == 'sma' ? 'selected' : '' }}>SMA/SMK</option>
                                <option value="diploma" {{ old('pendidikan', $user->pendidikan) == 'diploma' ? 'selected' : '' }}>Diploma</option>
                                <option value="s1" {{ old('pendidikan', $user->pendidikan) == 's1' ? 'selected' : '' }}>Sarjana (S1)</option>
                                <option value="s2" {{ old('pendidikan', $user->pendidikan) == 's2' ? 'selected' : '' }}>Magister (S2)</option>
                                <option value="s3" {{ old('pendidikan', $user->pendidikan) == 's3' ? 'selected' : '' }}>Doktor (S3)</option>
                            </select>
                            @error('pendidikan')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="institusi_pendidikan" class="block text-sm font-medium text-gray-700 mb-2">Institusi Pendidikan</label>
                            <input type="text" id="institusi_pendidikan" name="institusi_pendidikan" value="{{ old('institusi_pendidikan', $user->institusi_pendidikan) }}" placeholder="Universitas/Sekolah"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('institusi_pendidikan') border-red-500 @enderror">
                            @error('institusi_pendidikan')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="jurusan" class="block text-sm font-medium text-gray-700 mb-2">Jurusan</label>
                            <input type="text" id="jurusan" name="jurusan" value="{{ old('jurusan', $user->jurusan) }}" placeholder="Teknik Informatika, Manajemen, dll."
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('jurusan') border-red-500 @enderror">
                            @error('jurusan')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="tahun_lulus" class="block text-sm font-medium text-gray-700 mb-2">Tahun Lulus</label>
                            <input type="number" id="tahun_lulus" name="tahun_lulus" value="{{ old('tahun_lulus', $user->tahun_lulus) }}" placeholder="2024" min="1950" max="2030"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('tahun_lulus') border-red-500 @enderror">
                            @error('tahun_lulus')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Minat Belajar</label>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                            @php
                                $interests = ['Programming', 'Data Science', 'Design', 'Mobile Dev', 'AI/ML', 'Business', 'Marketing', 'Finance', 'UI/UX', 'DevOps', 'Cybersecurity', 'Blockchain'];
                                $userInterests = old('minat_belajar', $user->minat_belajar ?? []);
                            @endphp
                            @foreach($interests as $interest)
                                <label class="flex items-center">
                                    <input type="checkbox" name="minat_belajar[]" value="{{ $interest }}"
                                           {{ in_array($interest, $userInterests) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-primary focus:ring-primary">
                                    <span class="ml-2 text-sm text-gray-700">{{ $interest }}</span>
                                </label>
                            @endforeach
                        </div>
                        @error('minat_belajar')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Career Goals Section -->
                    <div class="border-t pt-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Tujuan Karir</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="short_term_goal" class="block text-sm font-medium text-gray-700 mb-2">Tujuan Jangka Pendek (1-2 tahun)</label>
                                <textarea id="short_term_goal" name="short_term_goal" rows="3" placeholder="Contoh: Menguasai React.js, Mendapat sertifikasi AWS, dll."
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('short_term_goal') border-red-500 @enderror">{{ old('short_term_goal', $user->career_goals['short_term'] ?? '') }}</textarea>
                                @error('short_term_goal')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="long_term_goal" class="block text-sm font-medium text-gray-700 mb-2">Tujuan Jangka Panjang (3-5 tahun)</label>
                                <textarea id="long_term_goal" name="long_term_goal" rows="3" placeholder="Contoh: Menjadi Tech Lead, Memulai startup, dll."
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('long_term_goal') border-red-500 @enderror">{{ old('long_term_goal', $user->career_goals['long_term'] ?? '') }}</textarea>
                                @error('long_term_goal')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Experience & Industry Section -->
                    <div class="border-t pt-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Pengalaman & Industri</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="experience_years" class="block text-sm font-medium text-gray-700 mb-2">Tahun Pengalaman Kerja</label>
                                <select id="experience_years" name="experience_years"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('experience_years') border-red-500 @enderror">
                                    <option value="">Pilih pengalaman</option>
                                    <option value="0" {{ old('experience_years', $user->experience_years) == '0' ? 'selected' : '' }}>Fresh Graduate / Belum Bekerja</option>
                                    <option value="1" {{ old('experience_years', $user->experience_years) == '1' ? 'selected' : '' }}>1 Tahun</option>
                                    <option value="2" {{ old('experience_years', $user->experience_years) == '2' ? 'selected' : '' }}>2 Tahun</option>
                                    <option value="3" {{ old('experience_years', $user->experience_years) == '3' ? 'selected' : '' }}>3 Tahun</option>
                                    <option value="4" {{ old('experience_years', $user->experience_years) == '4' ? 'selected' : '' }}>4 Tahun</option>
                                    <option value="5" {{ old('experience_years', $user->experience_years) == '5' ? 'selected' : '' }}>5 Tahun</option>
                                    <option value="6" {{ old('experience_years', $user->experience_years) == '6' ? 'selected' : '' }}>6-10 Tahun</option>
                                    <option value="10" {{ old('experience_years', $user->experience_years) == '10' ? 'selected' : '' }}>10+ Tahun</option>
                                </select>
                                @error('experience_years')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Industri yang Diminati</label>
                                <div class="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto border border-gray-300 rounded-lg p-3">
                                    @php
                                        $industries = ['Technology', 'Finance', 'Healthcare', 'Education', 'E-commerce', 'Gaming', 'Startups', 'Government', 'Manufacturing', 'Media', 'Consulting', 'Real Estate'];
                                        $userIndustries = old('industry_interests', $user->industry_interests ?? []);
                                    @endphp
                                    @foreach($industries as $industry)
                                        <label class="flex items-center">
                                            <input type="checkbox" name="industry_interests[]" value="{{ $industry }}"
                                                   {{ in_array($industry, $userIndustries) ? 'checked' : '' }}
                                                   class="rounded border-gray-300 text-primary focus:ring-primary">
                                            <span class="ml-2 text-sm text-gray-700">{{ $industry }}</span>
                                        </label>
                                    @endforeach
                                </div>
                                @error('industry_interests')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Work Preferences Section -->
                    <div class="border-t pt-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Preferensi Kerja</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Mode Kerja</label>
                                <div class="space-y-2">
                                    @php
                                        $workModes = ['remote' => 'Remote', 'hybrid' => 'Hybrid', 'onsite' => 'On-site'];
                                        $userWorkPrefs = old('work_preferences', $user->work_preferences ?? []);
                                    @endphp
                                    @foreach($workModes as $value => $label)
                                        <label class="flex items-center">
                                            <input type="checkbox" name="work_preferences[]" value="{{ $value }}"
                                                   {{ in_array($value, $userWorkPrefs) ? 'checked' : '' }}
                                                   class="rounded border-gray-300 text-primary focus:ring-primary">
                                            <span class="ml-2 text-sm text-gray-700">{{ $label }}</span>
                                        </label>
                                    @endforeach
                                </div>
                                @error('work_preferences')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Preferensi Lainnya</label>
                                <div class="space-y-2">
                                    @php
                                        $otherPrefs = ['flexible_hours' => 'Jam Kerja Fleksibel', 'team_collaboration' => 'Kerja Tim', 'leadership' => 'Posisi Leadership', 'mentoring' => 'Mentoring', 'learning_opportunities' => 'Kesempatan Belajar', 'creative_freedom' => 'Kebebasan Kreatif'];
                                    @endphp
                                    @foreach($otherPrefs as $value => $label)
                                        <label class="flex items-center">
                                            <input type="checkbox" name="work_preferences[]" value="{{ $value }}"
                                                   {{ in_array($value, $userWorkPrefs) ? 'checked' : '' }}
                                                   class="rounded border-gray-300 text-primary focus:ring-primary">
                                            <span class="ml-2 text-sm text-gray-700">{{ $label }}</span>
                                        </label>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Learning Preferences Section -->
                    <div class="border-t pt-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Preferensi Belajar</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Gaya Belajar</label>
                                <div class="space-y-2">
                                    @php
                                        $learningStyles = ['visual' => 'Visual (Video, Diagram)', 'auditory' => 'Auditory (Audio, Diskusi)', 'kinesthetic' => 'Kinesthetic (Praktek Langsung)', 'reading' => 'Reading (Teks, Artikel)'];
                                        $userLearningPrefs = old('learning_preferences', $user->learning_preferences ?? []);
                                    @endphp
                                    @foreach($learningStyles as $value => $label)
                                        <label class="flex items-center">
                                            <input type="checkbox" name="learning_preferences[]" value="{{ $value }}"
                                                   {{ in_array($value, $userLearningPrefs) ? 'checked' : '' }}
                                                   class="rounded border-gray-300 text-primary focus:ring-primary">
                                            <span class="ml-2 text-sm text-gray-700">{{ $label }}</span>
                                        </label>
                                    @endforeach
                                </div>
                                @error('learning_preferences')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Waktu Belajar Ideal</label>
                                <div class="space-y-2">
                                    @php
                                        $learningTimes = ['morning' => 'Pagi (06:00-12:00)', 'afternoon' => 'Siang (12:00-18:00)', 'evening' => 'Malam (18:00-24:00)', 'flexible' => 'Fleksibel'];
                                    @endphp
                                    @foreach($learningTimes as $value => $label)
                                        <label class="flex items-center">
                                            <input type="checkbox" name="learning_preferences[]" value="{{ $value }}"
                                                   {{ in_array($value, $userLearningPrefs) ? 'checked' : '' }}
                                                   class="rounded border-gray-300 text-primary focus:ring-primary">
                                            <span class="ml-2 text-sm text-gray-700">{{ $label }}</span>
                                        </label>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-4">
                        <button type="button" class="btn btn-outline">
                            Batal
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Simpan Perubahan
                        </button>
                    </div>
                </form>
                </div>

            <!-- Security Section -->
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mt-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Keamanan Akun</h3>

                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div>
                            <h4 class="font-medium text-gray-900">Password</h4>
                            <p class="text-sm text-gray-600">Terakhir diubah {{ $user->updated_at->format('d M Y') }}</p>
                        </div>
                        <button class="btn btn-outline btn-sm">
                            Ubah Password
                        </button>
                    </div>

                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div>
                            <h4 class="font-medium text-gray-900">Two-Factor Authentication</h4>
                            <p class="text-sm text-gray-600">Tambahkan lapisan keamanan ekstra untuk akun Anda</p>
                        </div>
                        <button class="btn btn-outline btn-sm">
                            Aktifkan 2FA
                        </button>
                    </div>

                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div>
                            <h4 class="font-medium text-gray-900">Sesi Login</h4>
                            <p class="text-sm text-gray-600">Kelola perangkat yang terhubung ke akun Anda</p>
                        </div>
                        <button class="btn btn-outline btn-sm">
                            Kelola Sesi
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
