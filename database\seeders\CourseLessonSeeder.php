<?php

namespace Database\Seeders;

use App\Models\CourseChapter;
use App\Models\CourseLesson;
use Illuminate\Database\Seeder;

class CourseLessonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all chapters to add lessons to
        $chapters = CourseChapter::with('course')->get();

        foreach ($chapters as $chapter) {
            $this->createLessonsForChapter($chapter);
        }
    }

    private function createLessonsForChapter(CourseChapter $chapter)
    {
        $lessons = [];

        // Create lessons based on chapter title
        if (str_contains($chapter->title, 'Pengenalan Programming')) {
            $lessons = [
                [
                    'title' => '1.1 Apa itu Programming?',
                    'description' => 'Pengenalan konsep dasar pemrograman',
                    'content' => 'Programming adalah proses membuat instruksi untuk komputer...',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration_minutes' => 15,
                    'type' => 'video',
                    'is_published' => true,
                    'is_preview' => true, // First lesson is always preview
                ],
                [
                    'title' => '1.2 Sejarah <PERSON>n',
                    'description' => 'Perkembangan bahasa pemrograman dari masa ke masa',
                    'content' => 'Sejarah pemrograman dimulai dari...',
                    'duration_minutes' => 12,
                    'type' => 'text',
                    'is_published' => true,
                    'is_preview' => false,
                ],
                [
                    'title' => '1.3 Quiz: Konsep Dasar',
                    'description' => 'Uji pemahaman tentang konsep dasar programming',
                    'duration_minutes' => 10,
                    'type' => 'quiz',
                    'is_published' => true,
                    'is_preview' => false,
                ],
            ];
        } elseif (str_contains($chapter->title, 'Variabel dan Tipe Data')) {
            $lessons = [
                [
                    'title' => '2.1 Mengenal Variabel',
                    'description' => 'Cara mendeklarasikan dan menggunakan variabel',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration_minutes' => 18,
                    'type' => 'video',
                    'is_published' => true,
                    'is_preview' => false,
                ],
                [
                    'title' => '2.2 Tipe Data Primitif',
                    'description' => 'Integer, float, string, dan boolean',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration_minutes' => 20,
                    'type' => 'video',
                    'is_published' => true,
                    'is_preview' => false,
                ],
                [
                    'title' => '2.3 Latihan: Membuat Variabel',
                    'description' => 'Praktik langsung membuat dan menggunakan variabel',
                    'duration_minutes' => 30,
                    'type' => 'assignment',
                    'is_published' => true,
                    'is_preview' => false,
                ],
            ];
        } elseif (str_contains($chapter->title, 'React Fundamentals')) {
            $lessons = [
                [
                    'title' => '1.1 Pengenalan React',
                    'description' => 'Apa itu React dan mengapa menggunakannya',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration_minutes' => 25,
                    'type' => 'video',
                    'is_published' => true,
                    'is_free' => true,
                    'is_preview' => true,
                ],
                [
                    'title' => '1.2 Setup Development Environment',
                    'description' => 'Menyiapkan lingkungan pengembangan React',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration_minutes' => 30,
                    'type' => 'video',
                    'is_published' => true,
                    'is_free' => true,
                    'is_preview' => false,
                ],
                [
                    'title' => '1.3 JSX Syntax',
                    'description' => 'Memahami sintaks JSX dalam React',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration_minutes' => 22,
                    'type' => 'video',
                    'is_published' => true,
                    'is_free' => false,
                    'is_preview' => false,
                ],
            ];
        } elseif (str_contains($chapter->title, 'HTML')) {
            $lessons = [
                [
                    'title' => '1.1 Struktur Dasar HTML',
                    'description' => 'Memahami struktur dokumen HTML',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration_minutes' => 20,
                    'type' => 'video',
                    'is_published' => true,
                    'is_free' => true,
                    'is_preview' => true,
                ],
                [
                    'title' => '1.2 HTML Tags dan Elements',
                    'description' => 'Mengenal berbagai tag HTML dan penggunaannya',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration_minutes' => 25,
                    'type' => 'video',
                    'is_published' => true,
                    'is_free' => true,
                    'is_preview' => false,
                ],
                [
                    'title' => '1.3 Praktik: Membuat Halaman Web Pertama',
                    'description' => 'Latihan membuat halaman web sederhana',
                    'duration_minutes' => 45,
                    'type' => 'assignment',
                    'is_published' => true,
                    'is_free' => true,
                    'is_preview' => false,
                ],
            ];
        } else {
            // Default lessons for other chapters
            $lessons = [
                [
                    'title' => 'Pengenalan Materi',
                    'description' => 'Pengenalan dasar materi dalam bab ini',
                    'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'duration_minutes' => 15,
                    'type' => 'video',
                    'is_published' => true,
                    'is_free' => $chapter->is_free,
                    'is_preview' => $chapter->is_free,
                ],
                [
                    'title' => 'Praktik dan Latihan',
                    'description' => 'Latihan untuk memperdalam pemahaman',
                    'duration_minutes' => 30,
                    'type' => 'assignment',
                    'is_published' => false,
                    'is_free' => false,
                    'is_preview' => false,
                ],
            ];
        }

        foreach ($lessons as $index => $lessonData) {
            CourseLesson::create([
                'course_id' => $chapter->course_id,
                'chapter_id' => $chapter->id,
                'title' => $lessonData['title'],
                'description' => $lessonData['description'],
                'content' => $lessonData['content'] ?? null,
                'video_url' => $lessonData['video_url'] ?? null,
                'duration_minutes' => $lessonData['duration_minutes'],
                'type' => $lessonData['type'],
                'sort_order' => $index + 1,
                'is_published' => $lessonData['is_published'],
                'is_free' => $chapter->course->is_free, // Inherit from course
                'is_preview' => $lessonData['is_preview'] ?? false,
            ]);
        }
    }
}
