@extends('layouts.app')

@section('title', 'Kurikulum - Ngambiskuy')

@section('content')
<div class="bg-gray-50 min-h-screen">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-primary to-secondary text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">Kurikulum Pembelajaran</h1>
                <p class="text-xl md:text-2xl mb-8 opacity-90">Temukan kursus terbaik untuk mengembangkan skill Anda</p>

                <!-- Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12">
                    <div class="text-center">
                        <div class="text-3xl font-bold">{{ number_format($stats['total_courses']) }}</div>
                        <div class="text-sm opacity-80">Total Kursus</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold">{{ number_format($stats['free_courses']) }}</div>
                        <div class="text-sm opacity-80">Kursus Gratis</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold">{{ number_format($stats['paid_courses']) }}</div>
                        <div class="text-sm opacity-80">Kursus Premium</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold">{{ number_format($stats['total_students']) }}+</div>
                        <div class="text-sm opacity-80">Total Siswa</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <form method="GET" action="{{ route('curriculum.index') }}" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div>
                        <input type="text"
                               name="search"
                               value="{{ request('search') }}"
                               placeholder="Cari kursus..."
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>

                    <!-- Category Filter -->
                    <div>
                        <select name="category" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">Semua Kategori</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->slug }}" {{ request('category') == $category->slug ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Level Filter -->
                    <div>
                        <select name="level" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">Semua Level</option>
                            <option value="beginner" {{ request('level') == 'beginner' ? 'selected' : '' }}>Pemula</option>
                            <option value="intermediate" {{ request('level') == 'intermediate' ? 'selected' : '' }}>Menengah</option>
                            <option value="advanced" {{ request('level') == 'advanced' ? 'selected' : '' }}>Lanjutan</option>
                        </select>
                    </div>

                    <!-- Price Filter -->
                    <div>
                        <select name="price_type" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">Semua Harga</option>
                            <option value="free" {{ request('price_type') == 'free' ? 'selected' : '' }}>Gratis</option>
                            <option value="paid" {{ request('price_type') == 'paid' ? 'selected' : '' }}>Berbayar</option>
                        </select>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-3">
                    <button type="submit" class="btn btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Cari Kursus
                    </button>
                    @if(request()->hasAny(['search', 'category', 'level', 'price_type']))
                        <a href="{{ route('curriculum.index') }}" class="btn btn-outline">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Reset Filter
                        </a>
                    @endif
                </div>
            </form>
        </div>
    </div>

    <!-- Courses Grid -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        @if($courses->count() > 0)
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($courses as $course)
                    <div class="bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300 overflow-hidden">
                        <div class="relative">
                            @if($course->thumbnail)
                                <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}" class="w-full h-48 object-cover">
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-primary to-secondary flex items-center justify-center">
                                    <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                </div>
                            @endif

                            <!-- Price Badge -->
                            @if($course->price == 0)
                                <span class="absolute top-3 right-3 bg-green-600 text-white text-xs px-2 py-1 rounded">GRATIS</span>
                            @else
                                <span class="absolute top-3 right-3 bg-primary text-white text-xs px-2 py-1 rounded">{{ $course->formatted_price }}</span>
                            @endif

                            <!-- Featured Badge -->
                            @if($course->is_featured)
                                <span class="absolute top-3 left-3 bg-yellow-500 text-white text-xs px-2 py-1 rounded">FEATURED</span>
                            @endif
                        </div>

                        <div class="p-6">
                            <div class="space-y-3">
                                <!-- Category -->
                                <span class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">{{ $course->category->name }}</span>

                                <!-- Title -->
                                <h3 class="text-lg font-semibold line-clamp-2">{{ $course->title }}</h3>

                                <!-- Description -->
                                <p class="text-gray-600 text-sm line-clamp-2">{{ $course->description }}</p>

                                <!-- Instructor -->
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                    </div>
                                    <span class="text-sm text-gray-600">{{ $course->tutor->name }}</span>
                                </div>

                                <!-- Course Stats -->
                                <div class="flex items-center justify-between text-sm text-gray-500">
                                    <div class="flex items-center space-x-4">
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            {{ $course->duration }}
                                        </span>
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                            {{ number_format($course->total_students) }}
                                        </span>
                                    </div>
                                    <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">{{ $course->level_indonesian }}</span>
                                </div>

                                <!-- Rating -->
                                @if($course->average_rating > 0)
                                    <div class="flex items-center space-x-1">
                                        <div class="flex items-center">
                                            @for($i = 1; $i <= 5; $i++)
                                                <svg class="w-4 h-4 {{ $i <= $course->average_rating ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                </svg>
                                            @endfor
                                        </div>
                                        <span class="text-sm text-gray-600">{{ number_format($course->average_rating, 1) }} ({{ number_format($course->total_reviews) }})</span>
                                    </div>
                                @endif

                                <!-- Action Button -->
                                <div class="pt-2">
                                    <a href="{{ route('course.show', $course) }}" class="btn btn-primary w-full">
                                        Lihat Detail
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-12">
                {{ $courses->links() }}
            </div>
        @else
            <!-- No Courses Found -->
            <div class="text-center py-16">
                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Tidak ada kursus ditemukan</h3>
                <p class="text-gray-600 mb-6">Coba ubah filter pencarian atau kata kunci Anda</p>
                <a href="{{ route('curriculum.index') }}" class="btn btn-primary">Lihat Semua Kursus</a>
            </div>
        @endif
    </div>
</div>
@endsection
