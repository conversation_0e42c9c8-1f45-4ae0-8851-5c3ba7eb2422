@extends('layouts.tutor')

@section('title', 'Buat Kursus Baru - Tutor Dashboard')

@section('content')
<!-- Success/Error Messages -->
@if(session('success'))
    <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
        {{ session('success') }}
    </div>
@endif

@if(session('error'))
    <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
        {{ session('error') }}
    </div>
@endif

@if($errors->any())
    <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
        <h4 class="font-medium">Ada kesalahan dalam form:</h4>
        <ul class="mt-2 list-disc list-inside">
            @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
<div class="p-6">
    <!-- <PERSON> Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Buat Kursus Baru</h1>
                <p class="text-gray-600 mt-1">Bagikan pengetahuan Anda dengan siswa di seluruh Indonesia</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{{ route('tutor.courses') }}" class="btn btn-outline">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Kembali ke Kursus
                </a>
            </div>
        </div>
    </div>

    <!-- AI Course Builder Option -->
    <div class="bg-gradient-to-r from-primary to-orange-500 rounded-lg p-6 mb-6 text-white border border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-xl font-bold mb-2">🤖 AI Course Builder</h2>
                <p class="text-white/90 mb-4">Biarkan AI membantu Anda membuat kurikulum yang terstruktur dan menarik dalam hitungan menit!</p>
                <ul class="text-white/90 text-sm space-y-1">
                    <li>✓ Otomatis membuat outline kursus</li>
                    <li>✓ Saran materi dan quiz</li>
                    <li>✓ Estimasi durasi pembelajaran</li>
                    <li>✓ Rekomendasi harga optimal</li>
                </ul>
            </div>
            <div class="text-center">
                <button class="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Gunakan AI Builder
                </button>
                <p class="text-white/80 text-xs mt-2">Hemat 80% waktu pembuatan</p>
            </div>
        </div>
    </div>

    <!-- Manual Course Creation Form -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">Atau Buat Manual</h2>
                <span class="text-sm text-gray-500">Langkah 1 dari 4</span>
            </div>

            <form action="{{ route('tutor.store-course') }}" method="POST" enctype="multipart/form-data" class="space-y-8">
                @csrf
                <!-- Basic Information -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Informasi Dasar</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="course_title" class="block text-sm font-medium text-gray-700 mb-2">Judul Kursus *</label>
                            <input type="text" id="course_title" name="course_title" value="{{ old('course_title') }}"
                                   placeholder="Contoh: Belajar React.js untuk Pemula"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('course_title') border-red-500 @enderror">
                            @error('course_title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="course_category" class="block text-sm font-medium text-gray-700 mb-2">Kategori *</label>
                            <select id="course_category" name="course_category"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('course_category') border-red-500 @enderror">
                                <option value="">Pilih kategori</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ old('course_category') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('course_category')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Course Description -->
                <div>
                    <label for="course_description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi Kursus *</label>
                    <textarea id="course_description" name="course_description" rows="4"
                              placeholder="Jelaskan apa yang akan dipelajari siswa dalam kursus ini..."
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('course_description') border-red-500 @enderror">{{ old('course_description') }}</textarea>
                    @error('course_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Course Details -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Detail Kursus</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="course_level" class="block text-sm font-medium text-gray-700 mb-2">Level *</label>
                            <select id="course_level" name="course_level"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('course_level') border-red-500 @enderror">
                                <option value="">Pilih level</option>
                                <option value="beginner" {{ old('course_level') == 'beginner' ? 'selected' : '' }}>Pemula</option>
                                <option value="intermediate" {{ old('course_level') == 'intermediate' ? 'selected' : '' }}>Menengah</option>
                                <option value="advanced" {{ old('course_level') == 'advanced' ? 'selected' : '' }}>Lanjutan</option>
                            </select>
                            @error('course_level')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="course_duration" class="block text-sm font-medium text-gray-700 mb-2">Estimasi Durasi</label>
                            <input type="text" id="course_duration" name="course_duration" value="{{ old('course_duration') }}"
                                   placeholder="Contoh: 20 jam"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('course_duration') border-red-500 @enderror">
                            @error('course_duration')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Kursus *</label>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="radio" name="course_type" value="free" {{ old('course_type', 'free') == 'free' ? 'checked' : '' }}
                                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300" onchange="togglePriceField()">
                                    <span class="ml-2 text-sm text-gray-700">Gratis</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="course_type" value="paid" {{ old('course_type') == 'paid' ? 'checked' : '' }}
                                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300" onchange="togglePriceField()">
                                    <span class="ml-2 text-sm text-gray-700">Berbayar</span>
                                </label>
                            </div>
                            @error('course_type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div id="price-field" class="{{ old('course_type', 'free') == 'free' ? 'hidden' : '' }}">
                            <label for="course_price" class="block text-sm font-medium text-gray-700 mb-2">Harga Kursus (IDR) *</label>
                            <input type="number" id="course_price" name="course_price" value="{{ old('course_price', 0) }}"
                                   placeholder="299000"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent @error('course_price') border-red-500 @enderror">
                            @error('course_price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Learning Objectives -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tujuan Pembelajaran</label>
                    <div class="space-y-3" id="learning-outcomes-container">
                        <input type="text" name="learning_outcomes[]" value="{{ old('learning_outcomes.0') }}" placeholder="Siswa akan mampu..."
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <input type="text" name="learning_outcomes[]" value="{{ old('learning_outcomes.1') }}" placeholder="Siswa akan memahami..."
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <input type="text" name="learning_outcomes[]" value="{{ old('learning_outcomes.2') }}" placeholder="Siswa akan dapat membuat..."
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <button type="button" onclick="addLearningOutcome()" class="mt-2 text-primary hover:text-primary-dark text-sm font-medium">
                        + Tambah tujuan pembelajaran
                    </button>
                    @error('learning_outcomes')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Prerequisites -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Prasyarat</label>
                    <div class="space-y-3" id="requirements-container">
                        <input type="text" name="requirements[]" value="{{ old('requirements.0') }}" placeholder="Pengetahuan dasar tentang..."
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <input type="text" name="requirements[]" value="{{ old('requirements.1') }}" placeholder="Pengalaman dengan..."
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <button type="button" onclick="addRequirement()" class="mt-2 text-primary hover:text-primary-dark text-sm font-medium">
                        + Tambah prasyarat
                    </button>
                    @error('requirements')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Target Audience -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Target Audience</label>
                    <div class="space-y-3" id="target-audience-container">
                        <input type="text" name="target_audience[]" value="{{ old('target_audience.0') }}" placeholder="Pemula yang ingin belajar..."
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <input type="text" name="target_audience[]" value="{{ old('target_audience.1') }}" placeholder="Profesional yang ingin meningkatkan..."
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <button type="button" onclick="addTargetAudience()" class="mt-2 text-primary hover:text-primary-dark text-sm font-medium">
                        + Tambah target audience
                    </button>
                    @error('target_audience')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Course Thumbnail -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Thumbnail Kursus</label>
                    <div id="thumbnail-upload-area" class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition-colors @error('thumbnail') border-red-500 @enderror">
                        <div id="thumbnail-placeholder">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="mt-4">
                                <label for="thumbnail" class="cursor-pointer">
                                    <span class="mt-2 block text-sm font-medium text-gray-900">
                                        Upload thumbnail kursus
                                    </span>
                                    <span class="mt-1 block text-sm text-gray-500">
                                        PNG, JPG, JPEG hingga 10MB
                                    </span>
                                </label>
                            </div>
                        </div>
                        <div id="thumbnail-preview" class="hidden">
                            <img id="preview-image" src="" alt="Preview" class="mx-auto max-h-48 rounded-lg">
                            <div class="mt-4">
                                <p id="file-name" class="text-sm font-medium text-gray-900"></p>
                                <button type="button" onclick="removeThumbnail()" class="mt-2 text-sm text-red-600 hover:text-red-800">
                                    Hapus gambar
                                </button>
                            </div>
                        </div>
                        <input id="thumbnail" name="thumbnail" type="file" class="sr-only" accept="image/jpeg,image/png,image/jpg">
                    </div>
                    @error('thumbnail')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-between pt-6 border-t border-gray-200">
                    <button type="button" class="btn btn-outline">
                        Simpan sebagai Draft
                    </button>
                    <button type="submit" class="btn btn-primary">
                        Lanjut ke Kurikulum
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>
            </form>
    </div>

    <!-- Tips Section -->
    <div class="bg-blue-50 rounded-lg p-6 mt-6 border border-blue-200">
        <h3 class="text-lg font-semibold text-blue-900 mb-3">💡 Tips Membuat Kursus yang Menarik</h3>
        <ul class="text-blue-800 space-y-2 text-sm">
            <li>• Gunakan judul yang jelas dan spesifik</li>
            <li>• Jelaskan manfaat konkret yang akan didapat siswa</li>
            <li>• Tentukan target audience yang spesifik</li>
            <li>• Gunakan thumbnail yang menarik dan profesional</li>
            <li>• Buat tujuan pembelajaran yang terukur</li>
        </ul>
    </div>
</div>

<script>
function togglePriceField() {
    const courseType = document.querySelector('input[name="course_type"]:checked').value;
    const priceField = document.getElementById('price-field');
    const priceInput = document.getElementById('course_price');

    if (courseType === 'free') {
        priceField.classList.add('hidden');
        priceInput.value = 0;
    } else {
        priceField.classList.remove('hidden');
        if (priceInput.value == 0) {
            priceInput.value = '';
        }
    }
}

function addLearningOutcome() {
    const container = document.getElementById('learning-outcomes-container');
    const input = document.createElement('input');
    input.type = 'text';
    input.name = 'learning_outcomes[]';
    input.placeholder = 'Siswa akan...';
    input.className = 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent';
    container.appendChild(input);
}

function addRequirement() {
    const container = document.getElementById('requirements-container');
    const input = document.createElement('input');
    input.type = 'text';
    input.name = 'requirements[]';
    input.placeholder = 'Pengetahuan tentang...';
    input.className = 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent';
    container.appendChild(input);
}

function addTargetAudience() {
    const container = document.getElementById('target-audience-container');
    const input = document.createElement('input');
    input.type = 'text';
    input.name = 'target_audience[]';
    input.placeholder = 'Orang yang...';
    input.className = 'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent';
    container.appendChild(input);
}

// File upload preview and validation
document.getElementById('thumbnail').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const placeholder = document.getElementById('thumbnail-placeholder');
    const preview = document.getElementById('thumbnail-preview');
    const previewImage = document.getElementById('preview-image');
    const fileName = document.getElementById('file-name');

    if (file) {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
        if (!allowedTypes.includes(file.type)) {
            alert('Format file harus JPG, PNG, atau JPEG');
            this.value = '';
            return;
        }

        // Validate file size (10MB = 10 * 1024 * 1024 bytes)
        if (file.size > 10 * 1024 * 1024) {
            alert('Ukuran file maksimal 10MB');
            this.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            previewImage.src = e.target.result;
            fileName.textContent = file.name;
            placeholder.classList.add('hidden');
            preview.classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    }
});

function removeThumbnail() {
    const thumbnailInput = document.getElementById('thumbnail');
    const placeholder = document.getElementById('thumbnail-placeholder');
    const preview = document.getElementById('thumbnail-preview');

    thumbnailInput.value = '';
    placeholder.classList.remove('hidden');
    preview.classList.add('hidden');
}

// Form submission validation and cleanup
document.querySelector('form').addEventListener('submit', function(e) {
    // Clean up empty array inputs before submission
    const arrayContainers = ['learning-outcomes-container', 'requirements-container', 'target-audience-container'];

    arrayContainers.forEach(containerId => {
        const container = document.getElementById(containerId);
        const inputs = container.querySelectorAll('input');

        inputs.forEach(input => {
            if (!input.value || input.value.trim() === '') {
                input.remove();
            }
        });
    });

    const requiredFields = [
        { id: 'course_title', name: 'Judul Kursus' },
        { id: 'course_category', name: 'Kategori' },
        { id: 'course_description', name: 'Deskripsi Kursus' },
        { id: 'course_level', name: 'Level' },
        { id: 'course_price', name: 'Harga' }
    ];

    let hasError = false;

    requiredFields.forEach(field => {
        const element = document.getElementById(field.id);
        if (!element.value.trim()) {
            alert(`${field.name} harus diisi`);
            element.focus();
            hasError = true;
            return false;
        }
    });

    if (hasError) {
        e.preventDefault();
        return false;
    }

    // Show loading state
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = 'Menyimpan...';

    // Re-enable button after 10 seconds as fallback
    setTimeout(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }, 10000);
});
</script>
@endsection
