<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Programming',
                'description' => 'Belajar bahasa pemrograman dan pengembangan software',
                'icon' => 'code',
                'color' => '#3B82F6',
                'sort_order' => 1,
            ],
            [
                'name' => 'Web Development',
                'description' => 'Pengembangan website dan aplikasi web',
                'icon' => 'globe',
                'color' => '#10B981',
                'sort_order' => 2,
            ],
            [
                'name' => 'Mobile Development',
                'description' => 'Pengembangan aplikasi mobile Android dan iOS',
                'icon' => 'device-mobile',
                'color' => '#8B5CF6',
                'sort_order' => 3,
            ],
            [
                'name' => 'Data Science',
                'description' => 'Analisis data, machine learning, dan AI',
                'icon' => 'chart-bar',
                'color' => '#F59E0B',
                'sort_order' => 4,
            ],
            [
                'name' => 'UI/UX Design',
                'description' => 'Desain antarmuka dan pengalaman pengguna',
                'icon' => 'color-swatch',
                'color' => '#EF4444',
                'sort_order' => 5,
            ],
            [
                'name' => 'Digital Marketing',
                'description' => 'Pemasaran digital dan strategi online',
                'icon' => 'speakerphone',
                'color' => '#06B6D4',
                'sort_order' => 6,
            ],
            [
                'name' => 'Business',
                'description' => 'Manajemen bisnis dan kewirausahaan',
                'icon' => 'briefcase',
                'color' => '#84CC16',
                'sort_order' => 7,
            ],
            [
                'name' => 'Cybersecurity',
                'description' => 'Keamanan siber dan perlindungan data',
                'icon' => 'shield-check',
                'color' => '#DC2626',
                'sort_order' => 8,
            ],
            [
                'name' => 'Other',
                'description' => 'Kategori lainnya yang tidak termasuk dalam kategori utama',
                'icon' => 'dots-horizontal',
                'color' => '#6B7280',
                'sort_order' => 9,
            ],
        ];

        foreach ($categories as $category) {
            Category::updateOrCreate(
                ['slug' => Str::slug($category['name'])],
                [
                    'name' => $category['name'],
                    'description' => $category['description'],
                    'icon' => $category['icon'],
                    'color' => $category['color'],
                    'is_active' => true,
                    'sort_order' => $category['sort_order'],
                ]
            );
        }
    }
}
