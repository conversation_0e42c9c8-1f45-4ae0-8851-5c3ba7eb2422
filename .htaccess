# Increase upload limits for video files
php_value upload_max_filesize 150M
php_value post_max_size 150M
php_value max_execution_time 600
php_value max_input_time 600
php_value memory_limit 512M

# Enable file uploads
php_flag file_uploads On

# Set maximum number of files that can be uploaded
php_value max_file_uploads 20

# Additional settings for better upload handling
php_value max_input_vars 3000
php_value default_socket_timeout 600

# Laravel specific settings
<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
