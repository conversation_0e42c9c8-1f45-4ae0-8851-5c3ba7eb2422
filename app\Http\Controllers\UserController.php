<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the user dashboard.
     */
    public function dashboard()
    {
        $user = Auth::user();

        // Mock data for dashboard stats - will be replaced with real data later
        $stats = [
            'enrolled_courses' => 0,
            'completed_courses' => 0,
            'certificates' => 0,
            'xp_points' => 0,
            'learning_streak' => 0,
            'total_hours' => 0
        ];

        // Mock data for recent activities
        $recentActivities = [];

        // Mock data for recommended courses
        $recommendedCourses = [
            [
                'id' => 1,
                'title' => 'Dasar-dasar Programming',
                'description' => 'Mulai dari nol hingga mahir',
                'icon' => 'code',
                'color' => 'blue',
                'price' => 'Gratis',
                'level' => 'Pemula'
            ],
            [
                'id' => 2,
                'title' => 'AI & Machine Learning',
                'description' => 'Teknologi masa depan',
                'icon' => 'brain',
                'color' => 'green',
                'price' => 'Premium',
                'level' => 'Menengah'
            ],
            [
                'id' => 3,
                'title' => 'UI/UX Design',
                'description' => 'Desain yang user-friendly',
                'icon' => 'palette',
                'color' => 'purple',
                'price' => 'Premium',
                'level' => 'Pemula'
            ]
        ];

        return view('user.dashboard', compact('user', 'stats', 'recentActivities', 'recommendedCourses'));
    }

    /**
     * Show the user profile page.
     */
    public function profile()
    {
        $user = Auth::user();
        return view('user.profile', compact('user'));
    }

    /**
     * Show the user courses page.
     */
    public function courses()
    {
        $user = Auth::user();

        // Mock data for enrolled courses (will be implemented with enrollment system)
        $enrolledCourses = [];

        // Get available courses from database
        $availableCourses = \App\Models\Course::with(['tutor', 'category'])
            ->published()
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();

        return view('user.courses', compact('user', 'enrolledCourses', 'availableCourses'));
    }

    /**
     * Show the user progress page.
     */
    public function progress()
    {
        $user = Auth::user();

        // Mock data for learning progress
        $progressData = [
            'overall_progress' => 0,
            'courses_in_progress' => [],
            'completed_modules' => 0,
            'total_modules' => 0,
            'learning_path' => []
        ];

        return view('user.progress', compact('user', 'progressData'));
    }

    /**
     * Show the user certificates page.
     */
    public function certificates()
    {
        $user = Auth::user();

        // Mock data for certificates
        $certificates = [];

        return view('user.certificates', compact('user', 'certificates'));
    }

    /**
     * Show the user settings page.
     */
    public function settings()
    {
        $user = Auth::user();
        return view('user.settings', compact('user'));
    }
}
