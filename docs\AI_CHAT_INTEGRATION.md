# AI Chat Component - Ngambiskuy Platform

## Overview

The AI Chat Component is a comprehensive chat interface designed for the Ngambiskuy platform, featuring the **Intelligent Course Engine (ICE)** that will be powered by Google's Gemini AI. This component provides personalized course recommendations, career path analysis, and learning guidance across all pages of the platform.

## Features

### Current Implementation
- ✅ Responsive chat interface with modern design
- ✅ Floating chat button with notification system
- ✅ Real-time typing indicators
- ✅ Message history with timestamps
- ✅ Quick action buttons for common queries
- ✅ Character counter and input validation
- ✅ Mobile-responsive design
- ✅ Smooth animations and transitions
- ✅ Local storage for message persistence

### Planned AI Features (Gemini Integration)

#### 1. Intelligent Course Engine (ICE)
**For Teachers:**
- Automated curriculum generation from uploaded materials (PDFs, PPTs, videos)
- NLP-powered content analysis and structuring
- Quiz and assignment suggestions based on content
- Prerequisite course recommendations
- Content localization in Bahasa Indonesia

**For Students:**
- Personalized learning path creation
- Skill gap analysis and recommendations
- Dynamic course adjustments based on performance
- Career-focused roadmaps (e.g., "Frontend Developer in 3 months")

#### 2. AI Teaching Assistants
- 24/7 student support in Bahasa Indonesia
- Real-time feedback on quizzes and assignments
- Context-aware responses based on course content
- Cultural relevance for Indonesian learners

#### 3. Career Path Predictor
- Skills assessment and job role alignment
- Market trend analysis integration
- LinkedIn/JobStreet API integration for real-time job data
- Salary benchmarking and career progression insights

## File Structure

```
resources/
├── views/
│   └── components/
│       └── ai-chat.blade.php          # Main chat component
├── js/
│   └── ai-chat.js                     # Chat functionality and AI integration
└── css/
    └── app.css                        # Styles (included in main CSS)

docs/
└── AI_CHAT_INTEGRATION.md             # This documentation
```

## Component Architecture

### Frontend Components
1. **Chat Toggle Button** - Floating action button with notification badge
2. **Chat Window** - Main chat interface with header, messages, and input
3. **Message System** - User and AI message rendering with timestamps
4. **Quick Actions** - Predefined action buttons for common queries
5. **Typing Indicator** - Visual feedback during AI processing

### JavaScript Class Structure
```javascript
class NgambiskuyAIChat {
    constructor()           // Initialize chat system
    init()                 // Setup event listeners and load user profile
    toggleChat()           // Show/hide chat window
    sendMessage()          // Handle user message sending
    callAIService()        // Integration point for Gemini AI
    addMessage()           // Add messages to chat history
    generateResponse()     // Placeholder AI response logic
}
```

## Integration Points

### 1. Backend API Endpoints (To Be Created)
```
POST /api/ai-chat
- Handles chat messages and returns AI responses
- Integrates with Gemini AI API
- Manages conversation context and user profiling

GET /api/user-profile
- Returns user skills, interests, and learning history
- Used for personalized recommendations

POST /api/course-recommendations
- Generates course recommendations based on user profile
- Integrates with ICE algorithms

POST /api/career-analysis
- Provides career path analysis and predictions
- Integrates with job market data
```

### 2. Database Schema (Recommended)
```sql
-- Chat conversations
CREATE TABLE ai_conversations (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    session_id VARCHAR(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- Individual messages
CREATE TABLE ai_messages (
    id BIGINT PRIMARY KEY,
    conversation_id BIGINT,
    sender ENUM('user', 'ai'),
    content TEXT,
    metadata JSON,
    created_at TIMESTAMP
);

-- User AI profiles
CREATE TABLE user_ai_profiles (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    skills JSON,
    interests JSON,
    career_goals JSON,
    learning_preferences JSON,
    updated_at TIMESTAMP
);
```

## Gemini AI Integration Guide

### 1. Setup Gemini AI API
```javascript
// In resources/js/ai-chat.js - callAIService method
async callAIService(message) {
    try {
        const response = await fetch('/api/ai-chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                message: message,
                context: this.getConversationContext(),
                user_profile: this.userProfile,
                intent: this.detectIntent(message)
            })
        });
        
        const data = await response.json();
        return data.response;
    } catch (error) {
        throw new Error('Failed to get AI response');
    }
}
```

### 2. Backend Controller (Laravel)
```php
// app/Http/Controllers/AIChatController.php
class AIChatController extends Controller
{
    public function chat(Request $request)
    {
        $message = $request->input('message');
        $context = $request->input('context', []);
        $userProfile = $request->input('user_profile', []);
        
        // Call Gemini AI API
        $geminiResponse = $this->callGeminiAPI($message, $context, $userProfile);
        
        // Store conversation
        $this->storeConversation($request->user(), $message, $geminiResponse);
        
        return response()->json([
            'response' => $geminiResponse,
            'suggestions' => $this->generateSuggestions($geminiResponse)
        ]);
    }
    
    private function callGeminiAPI($message, $context, $userProfile)
    {
        // Implement Gemini AI API call
        // Include ICE prompts and context
    }
}
```

### 3. Environment Configuration
```env
# Add to .env file
GEMINI_API_KEY=your_gemini_api_key
GEMINI_MODEL=gemini-pro
GEMINI_ENDPOINT=https://generativelanguage.googleapis.com/v1beta/models/
```

## Customization Options

### 1. Styling
The component uses Tailwind CSS classes and can be customized by modifying:
- Color schemes in the gradient classes
- Animation timings in CSS transitions
- Responsive breakpoints for mobile optimization

### 2. Behavior
- Message history persistence (currently localStorage)
- Notification system timing and triggers
- Quick action button customization
- Typing indicator duration

### 3. AI Responses
- Response templates for different intents
- Conversation context management
- User profiling and personalization
- Multi-language support

## Performance Considerations

1. **Lazy Loading**: Chat component only initializes when needed
2. **Message Pagination**: Implement pagination for long conversations
3. **Caching**: Cache AI responses for common queries
4. **Rate Limiting**: Implement rate limiting for API calls
5. **Offline Support**: Provide offline responses for basic queries

## Security Considerations

1. **Input Sanitization**: All user inputs are escaped and validated
2. **CSRF Protection**: All API calls include CSRF tokens
3. **Rate Limiting**: Prevent spam and abuse
4. **Data Privacy**: Secure handling of conversation data
5. **API Key Security**: Secure storage of Gemini API credentials

## Testing Strategy

### 1. Unit Tests
- Test chat component initialization
- Test message sending and receiving
- Test user input validation
- Test local storage functionality

### 2. Integration Tests
- Test AI API integration
- Test database operations
- Test user profile integration
- Test notification system

### 3. E2E Tests
- Test complete chat flow
- Test responsive design
- Test accessibility features
- Test performance under load

## Deployment Checklist

- [ ] Configure Gemini AI API credentials
- [ ] Set up database tables
- [ ] Configure rate limiting
- [ ] Test responsive design
- [ ] Verify accessibility compliance
- [ ] Performance testing
- [ ] Security audit
- [ ] User acceptance testing

## Future Enhancements

1. **Voice Integration**: Add voice input/output capabilities
2. **File Sharing**: Allow users to share documents with AI
3. **Screen Sharing**: Integration with course content
4. **Multi-language**: Support for multiple Indonesian languages
5. **Analytics**: Detailed conversation analytics and insights
6. **Integration**: Connect with LMS and course management systems

## Support and Maintenance

For technical support or feature requests related to the AI Chat component:
- Email: <EMAIL>
- Documentation: This file and inline code comments
- Version Control: Track changes through Git commits

---

**Last Updated**: December 2024
**Version**: 1.0.0
**Maintainer**: Ngambiskuy Development Team
